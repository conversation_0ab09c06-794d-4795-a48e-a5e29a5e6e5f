# 🤖 Dual AI Model Setup Guide

Your Stress AI now supports **both online and offline AI models** for maximum flexibility and reliability!

## 🎯 Features

### ✅ **Dual Model Support**
- **🌐 Online Model**: DeepSeek Chat v3 via OpenRouter API (advanced responses)
- **💻 Offline Model**: Llama 3.2:1b via local Ollama (privacy & offline access)
- **🔄 Auto Mode**: Automatically uses the best available model

### ✅ **Smart Fallback System**
- If online model fails → automatically switches to offline
- If offline model unavailable → uses online model
- Real-time model status checking

### ✅ **User Control**
- Model selector in AI Chat interface
- Switch between models anytime
- Visual status indicators

## 🚀 Quick Start

1. **Start the server:**
   ```bash
   node server.js
   ```

2. **Open your browser:**
   Navigate to `http://localhost:3000`

3. **Go to AI Chat:**
   - Click "AI Chat" in the navigation
   - See the model selector at the top
   - Choose your preferred model or use "Auto"

## 🔧 Setup Instructions

### 🌐 Online Model (DeepSeek via OpenRouter)

**Already configured!** Your API key is set up:
- API Key: `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`
- Model: `deepseek/deepseek-chat-v3-0324:free`
- Provider: OpenRouter.ai

**Benefits:**
- ✅ Advanced reasoning capabilities
- ✅ Better code generation
- ✅ More detailed responses
- ✅ Latest AI technology
- ✅ No local setup required

### 💻 Offline Model (Llama 3.2:1b via Ollama)

**Setup Required:**

1. **Install Ollama:**
   - Windows: Download from https://ollama.ai
   - macOS: `brew install ollama`
   - Linux: `curl -fsSL https://ollama.ai/install.sh | sh`

2. **Download the model:**
   ```bash
   ollama pull llama3.2:1b
   ```

3. **Start Ollama service:**
   ```bash
   ollama serve
   ```

**Benefits:**
- ✅ Complete privacy (no data sent online)
- ✅ Works offline
- ✅ Fast responses
- ✅ No API costs
- ✅ Always available

## 🎮 How to Use

### 1. **Auto Mode (Recommended)**
- Select "Auto (Best Available)" in the model selector
- System automatically chooses the best working model
- Seamless fallback if one model fails

### 2. **Online Mode**
- Select "Online (DeepSeek)" for advanced AI responses
- Best for complex questions, coding, and detailed analysis
- Requires internet connection

### 3. **Offline Mode**
- Select "Offline (Llama 3.2)" for private, local AI
- Perfect for sensitive conversations
- Works without internet

### 4. **Model Status Indicators**
- 🟢 **Green**: Model available and working
- 🔵 **Blue**: Offline model ready
- 🟡 **Yellow**: Checking status
- 🔴 **Red**: Model unavailable

## 🔍 Testing Your Setup

Run the test script to verify everything works:

```bash
node test_models.js
```

This will:
- ✅ Check model availability
- ✅ Test online model responses
- ✅ Test offline model responses
- ✅ Verify auto mode functionality
- ✅ Test preference switching

## 🛠️ API Endpoints

### Model Status
```bash
GET /api/model/status
```
Returns current model availability and preferences.

### Change Model Preference
```bash
POST /api/model/preference
Content-Type: application/json

{
  "preference": "auto|online|offline"
}
```

### Chat with Specific Model
```bash
POST /api/chat/completions
Content-Type: application/json

{
  "messages": [...],
  "modelPreference": "auto|online|offline",
  "stressData": {...}
}
```

## 🔧 Troubleshooting

### Online Model Issues
- **"Online unavailable"**: Check internet connection
- **API errors**: Verify OpenRouter API key is valid
- **Rate limits**: Free tier has usage limits

### Offline Model Issues
- **"Offline unavailable"**: Install and start Ollama
- **Model not found**: Run `ollama pull llama3.2:1b`
- **Connection refused**: Start Ollama service with `ollama serve`

### General Issues
- **No models available**: Check both online and offline setup
- **Slow responses**: Offline model may be loading
- **Error messages**: Check browser console for details

## 📊 Model Comparison

| Feature | Online (DeepSeek) | Offline (Llama 3.2) |
|---------|------------------|---------------------|
| **Response Quality** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Speed** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Privacy** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Offline Access** | ❌ | ✅ |
| **Setup Required** | ❌ | ✅ |
| **Cost** | Free tier | Free |

## 🎉 Enjoy Your Enhanced AI!

Your stress AI now has the best of both worlds:
- 🌐 **Online**: Advanced AI when you need it
- 💻 **Offline**: Private AI that's always available
- 🔄 **Auto**: Smart switching for seamless experience

The system will automatically handle model selection and fallbacks, so you can focus on getting the help you need for stress management!
