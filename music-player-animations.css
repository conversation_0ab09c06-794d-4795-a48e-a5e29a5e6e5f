/* music-player-animations.css - Enhanced animations for the music player */

/* Volume control styling and animations */
.ai-volume-control {
    position: relative;
    padding: 10px 0;
    margin-top: 5px;
}

.ai-volume-control label {
    display: inline-block;
    margin-right: 10px;
    font-weight: 500;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.ai-volume-control:hover label {
    color: #2ecc71;
    transform: scale(1.05);
}

/* Custom volume slider styling */
.ai-volume-control input[type="range"] {
    -webkit-appearance: none;
    width: 100%;
    height: 6px;
    background: linear-gradient(to right, #2ecc71 0%, #2ecc71 70%, #ecf0f1 70%, #ecf0f1 100%);
    border-radius: 3px;
    outline: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
}

.ai-volume-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #2ecc71;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
}

.ai-volume-control input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border: none;
    border-radius: 50%;
    background: #2ecc71;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
}

.ai-volume-control input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.8);
}

.ai-volume-control input[type="range"]::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.8);
}

/* Volume indicator */
.ai-volume-control:after {
    content: '';
    position: absolute;
    right: -30px;
    top: 50%;
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232ecc71"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.ai-volume-control:hover:after {
    opacity: 0.7;
    right: -25px;
}

/* Muted state */
.ai-volume-control.muted input[type="range"] {
    background: linear-gradient(to right, #e74c3c 0%, #e74c3c 0%, #ecf0f1 0%, #ecf0f1 100%);
}

.ai-volume-control.muted input[type="range"]::-webkit-slider-thumb {
    background: #e74c3c;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
}

.ai-volume-control.muted input[type="range"]::-moz-range-thumb {
    background: #e74c3c;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
}

.ai-volume-control.muted:after {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e74c3c"><path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/></svg>');
}

/* Play button animations */
.play-btn {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(145deg, #2ecc71, #27ae60);
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

.play-btn:before {
    content: '▶';
    font-size: 20px;
    color: white;
    transition: all 0.3s ease;
}

.play-btn.playing:before {
    content: '■';
    font-size: 16px;
}

.play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(46, 204, 113, 0.4);
}

.play-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 5px rgba(46, 204, 113, 0.3);
}

.play-btn:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.play-btn:hover:after {
    opacity: 1;
}

.play-btn.playing {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.3);
}

.play-btn.playing:hover {
    box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4);
}

/* Progress bar animations */
.ai-player-progress {
    margin: 15px 0;
}

.progress-bar {
    height: 8px;
    background: rgba(236, 240, 241, 0.5);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    transition: height 0.3s ease;
}

.progress-bar:hover {
    height: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2ecc71, #3498db);
    background-size: 200% 100%;
    animation: progressGradient 2s linear infinite;
    border-radius: 4px;
    position: relative;
}

@keyframes progressGradient {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

.progress-fill:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 100%;
    background: white;
    opacity: 0.3;
    border-radius: 4px;
    transform: translateX(4px);
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.progress-bar:hover .progress-fill:after {
    transform: translateX(0);
    opacity: 0.5;
}

/* Time display animation */
#aiMusicTime {
    font-family: 'Consolas', monospace;
    font-size: 0.9em;
    color: #7f8c8d;
    transition: all 0.3s ease;
}

.ai-player-progress:hover #aiMusicTime {
    color: #2c3e50;
    font-weight: 600;
    transform: scale(1.05);
}

/* Track info animations */
.ai-track-info {
    position: relative;
    padding-left: 10px;
    transition: all 0.3s ease;
}

.ai-track-info:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: #2ecc71;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.ai-track-info:hover:before {
    width: 5px;
    background: #3498db;
}

.ai-track-info h4 {
    transition: all 0.3s ease;
}

.ai-track-info:hover h4 {
    transform: translateX(3px);
    color: #3498db;
}
