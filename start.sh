#!/bin/bash

# Stress AI Auto-Start Script for Linux/macOS
echo "🚀 Starting Stress AI Application..."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    read -p "Press Enter to exit"
    exit 1
fi

NODE_VERSION=$(node --version)
echo "✅ Node.js version: $NODE_VERSION"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the project directory."
    read -p "Press Enter to exit"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        read -p "Press Enter to exit"
        exit 1
    fi
    echo "✅ Dependencies installed successfully"
fi

# Check if Ollama is running (optional)
if curl -s "http://localhost:11434/api/tags" > /dev/null 2>&1; then
    echo "✅ Ollama is running (offline AI available)"
else
    echo "⚠️  Ollama not detected (online AI only)"
    echo "   To enable offline AI: Install Ollama and run 'ollama pull llama3.2:1b'"
fi

# Start the server
echo ""
echo "🌟 Starting server..."
echo ""
echo "🎯 Your Stress AI will be available at: http://localhost:3000"
echo "🔄 Press Ctrl+C to stop the server"
echo ""

# Open browser automatically after a short delay (macOS/Linux)
if command -v open &> /dev/null; then
    # macOS
    (sleep 3 && open "http://localhost:3000") &
elif command -v xdg-open &> /dev/null; then
    # Linux
    (sleep 3 && xdg-open "http://localhost:3000") &
fi

# Start the Node.js server
npm start
