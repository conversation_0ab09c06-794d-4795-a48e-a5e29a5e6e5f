/**
 * audio-utils.js - Enhanced utilities for audio format detection and compatibility
 * This version includes improved browser detection and format handling
 */

// Audio format detection and compatibility utilities
const AudioUtils = {
    // Store browser information for better format selection
    browserInfo: {
        name: '',
        version: '',
        isMobile: false,
        isIOS: false,
        isAndroid: false,
        isChrome: false,
        isSafari: false,
        isFirefox: false,
        isEdge: false,
        isIE: false
    },

    /**
     * Initialize the audio utilities and detect browser
     */
    init() {
        this.detectBrowser();
        this.logSupportedFormats();
        console.log('AudioUtils initialized with browser detection:', this.browserInfo);
        return this;
    },

    /**
     * Detect browser type and version
     */
    detectBrowser() {
        const ua = navigator.userAgent;

        // Detect mobile
        this.browserInfo.isMobile = /Mobi|Android|iPhone|iPad|iPod/i.test(ua);

        // Detect OS
        this.browserInfo.isIOS = /iPad|iPhone|iPod/.test(ua) ||
            (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
        this.browserInfo.isAndroid = /Android/.test(ua);

        // Detect browsers
        this.browserInfo.isChrome = /Chrome/.test(ua) && !/Edge/.test(ua);
        this.browserInfo.isSafari = /Safari/.test(ua) && !/Chrome/.test(ua);
        this.browserInfo.isFirefox = /Firefox/.test(ua);
        this.browserInfo.isEdge = /Edge|Edg/.test(ua);
        this.browserInfo.isIE = /Trident|MSIE/.test(ua);

        // Get browser name
        if (this.browserInfo.isChrome) this.browserInfo.name = 'Chrome';
        else if (this.browserInfo.isSafari) this.browserInfo.name = 'Safari';
        else if (this.browserInfo.isFirefox) this.browserInfo.name = 'Firefox';
        else if (this.browserInfo.isEdge) this.browserInfo.name = 'Edge';
        else if (this.browserInfo.isIE) this.browserInfo.name = 'Internet Explorer';
        else this.browserInfo.name = 'Unknown';

        // Get version (simplified)
        const match = ua.match(/(Chrome|Safari|Firefox|Edge|MSIE|Trident(?=\/))\/?\s*(\d+)/i) || [];
        this.browserInfo.version = match[2] || '';
    },

    /**
     * Check if a specific audio format is supported by the browser
     * @param {string} mimeType - The MIME type to check (e.g., 'audio/mp3', 'audio/ogg')
     * @returns {boolean} - Whether the format is supported
     */
    isFormatSupported(mimeType) {
        const audio = document.createElement('audio');
        const result = audio.canPlayType(mimeType);
        return result !== '' && result !== 'no';
    },

    /**
     * Get all supported audio formats for the current browser
     * @returns {Object} - Object with format support information
     */
    getSupportedFormats() {
        const audio = document.createElement('audio');

        // Test with more specific codec information for better accuracy
        return {
            mp3: audio.canPlayType('audio/mpeg;').replace(/^no$/, ''),
            mp4: audio.canPlayType('audio/mp4; codecs="mp4a.40.2"').replace(/^no$/, ''),
            m4a: audio.canPlayType('audio/x-m4a;').replace(/^no$/, '') ||
                 audio.canPlayType('audio/aac;').replace(/^no$/, ''),
            ogg: audio.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''),
            oga: audio.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/, ''),
            opus: audio.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/, '') ||
                  audio.canPlayType('audio/opus;').replace(/^no$/, ''),
            wav: audio.canPlayType('audio/wav; codecs="1"').replace(/^no$/, ''),
            aac: audio.canPlayType('audio/aac;').replace(/^no$/, ''),
            webm: audio.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/, '')
        };
    },

    /**
     * Get recommended formats based on browser detection
     * @returns {Array} - Array of recommended format extensions in order of preference
     */
    getRecommendedFormats() {
        // Default order for most browsers
        let formatOrder = ['mp3', 'aac', 'm4a', 'ogg', 'wav', 'webm'];

        // Adjust based on browser
        if (this.browserInfo.isFirefox) {
            formatOrder = ['ogg', 'opus', 'mp3', 'wav', 'aac', 'm4a', 'webm'];
        } else if (this.browserInfo.isSafari || this.browserInfo.isIOS) {
            formatOrder = ['m4a', 'aac', 'mp3', 'wav'];
        } else if (this.browserInfo.isChrome) {
            formatOrder = ['mp3', 'ogg', 'webm', 'm4a', 'aac', 'wav'];
        } else if (this.browserInfo.isEdge) {
            formatOrder = ['mp3', 'm4a', 'aac', 'wav'];
        }

        // Filter by actually supported formats
        const supportedFormats = this.getSupportedFormats();
        return formatOrder.filter(format => supportedFormats[format]);
    },

    /**
     * Log supported audio formats to console
     */
    logSupportedFormats() {
        const formats = this.getSupportedFormats();
        const recommended = this.getRecommendedFormats();
        console.log('Supported audio formats:', formats);
        console.log('Recommended formats for this browser:', recommended);
        return formats;
    },

    /**
     * Get alternative URLs for different audio formats
     * @param {string} baseUrl - The base URL without extension
     * @returns {Object} - Object with URLs for different formats
     */
    getMultiFormatUrls(baseUrl) {
        // Remove any existing extension
        const urlWithoutExt = baseUrl.replace(/\.(mp3|ogg|oga|opus|wav|aac|m4a|webm)$/, '');

        return {
            mp3: `${urlWithoutExt}.mp3`,
            ogg: `${urlWithoutExt}.ogg`,
            oga: `${urlWithoutExt}.oga`,
            opus: `${urlWithoutExt}.opus`,
            wav: `${urlWithoutExt}.wav`,
            aac: `${urlWithoutExt}.aac`,
            m4a: `${urlWithoutExt}.m4a`,
            webm: `${urlWithoutExt}.webm`
        };
    },

    /**
     * Create an audio element with multiple sources for better compatibility
     * @param {string|Object} source - The primary audio URL or an object with format URLs
     * @param {boolean} loop - Whether the audio should loop
     * @param {number} volume - Initial volume (0-1)
     * @returns {HTMLAudioElement} - Audio element with multiple sources
     */
    createMultiSourceAudio(source, loop = false, volume = 0.7) {
        // Create audio element
        const audio = document.createElement('audio');
        audio.preload = 'auto';
        audio.loop = loop;
        audio.volume = volume;

        // Get recommended formats for this browser
        const recommendedFormats = this.getRecommendedFormats();
        console.log('Creating audio with recommended formats:', recommendedFormats);

        // Handle different source types
        if (typeof source === 'string') {
            // Single URL string provided
            this.addSourcesFromUrl(audio, source);
        } else if (typeof source === 'object') {
            // Object with format URLs provided
            this.addSourcesFromObject(audio, source, recommendedFormats);
        }

        // Add error handling
        audio.addEventListener('error', (e) => {
            console.error('Audio error in createMultiSourceAudio:', e);
            this.handleAudioError(audio, source);
        });

        return audio;
    },

    /**
     * Add sources to audio element from a single URL
     * @param {HTMLAudioElement} audio - The audio element
     * @param {string} url - The primary URL
     */
    addSourcesFromUrl(audio, url) {
        // Try to determine the base URL without extension
        const baseUrl = url.substring(0, url.lastIndexOf('.')) || url;
        const formatUrls = this.getMultiFormatUrls(baseUrl);
        const recommendedFormats = this.getRecommendedFormats();

        // First try the primary URL directly
        if (this.canPlayUrl(url)) {
            audio.src = url;
            console.log('Using primary URL directly:', url);
            return;
        }

        // Try to add sources for each recommended format
        let sourcesAdded = false;

        // Create source elements for each format
        for (const format of recommendedFormats) {
            if (formatUrls[format]) {
                const source = document.createElement('source');
                source.src = formatUrls[format];
                source.type = this.getTypeFromFormat(format);
                audio.appendChild(source);
                sourcesAdded = true;
                console.log(`Added ${format} source:`, formatUrls[format]);
            }
        }

        // If no sources were added, use the primary URL as fallback
        if (!sourcesAdded) {
            audio.src = url;
            console.warn('No compatible sources found, using primary URL as fallback:', url);
        }
    },

    /**
     * Add sources to audio element from an object with format URLs
     * @param {HTMLAudioElement} audio - The audio element
     * @param {Object} sourceObject - Object with format URLs
     * @param {Array} recommendedFormats - Array of recommended formats
     */
    addSourcesFromObject(audio, sourceObject, recommendedFormats) {
        let sourcesAdded = false;

        // Try each recommended format
        for (const format of recommendedFormats) {
            if (sourceObject[format]) {
                const source = document.createElement('source');
                source.src = sourceObject[format];
                source.type = this.getTypeFromFormat(format);
                audio.appendChild(source);
                sourcesAdded = true;
                console.log(`Added ${format} source from object:`, sourceObject[format]);
            }
        }

        // If no sources were added but there's a fallback, use it
        if (!sourcesAdded && sourceObject.fallback) {
            audio.src = sourceObject.fallback;
            console.log('Using fallback URL:', sourceObject.fallback);
        }
        // If still no sources, try any available format
        else if (!sourcesAdded) {
            for (const format in sourceObject) {
                if (sourceObject[format] && typeof sourceObject[format] === 'string') {
                    audio.src = sourceObject[format];
                    console.warn(`Using first available format (${format}):`, sourceObject[format]);
                    break;
                }
            }
        }
    },

    /**
     * Handle audio element errors by trying alternative sources
     * @param {HTMLAudioElement} audio - The audio element
     * @param {string|Object} originalSource - The original source
     */
    handleAudioError(audio, originalSource) {
        console.log('Handling audio error, trying alternatives');

        // If we have a fallback URL in the original source object, try it
        if (typeof originalSource === 'object' && originalSource.fallback) {
            console.log('Trying fallback URL:', originalSource.fallback);
            audio.src = originalSource.fallback;
            audio.load();
            return;
        }

        // Try a completely different source format - use a reliable CDN source
        const fallbackUrl = 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3';
        console.log('Trying emergency fallback URL:', fallbackUrl);
        audio.src = fallbackUrl;
        audio.load();
    },

    /**
     * Get MIME type from format extension
     * @param {string} format - Format extension (mp3, ogg, etc.)
     * @returns {string} - MIME type
     */
    getTypeFromFormat(format) {
        switch (format) {
            case 'mp3': return 'audio/mpeg';
            case 'ogg': return 'audio/ogg; codecs="vorbis"';
            case 'oga': return 'audio/ogg; codecs="vorbis"';
            case 'opus': return 'audio/ogg; codecs="opus"';
            case 'wav': return 'audio/wav; codecs="1"';
            case 'aac': return 'audio/aac';
            case 'm4a': return 'audio/mp4; codecs="mp4a.40.2"';
            case 'webm': return 'audio/webm; codecs="vorbis"';
            default: return '';
        }
    },

    /**
     * Check if the browser can play a URL based on its extension
     * @param {string} url - The URL to check
     * @returns {boolean} - Whether the URL can be played
     */
    canPlayUrl(url) {
        if (!url) return false;

        const audio = document.createElement('audio');
        const extension = url.split('.').pop().toLowerCase();
        const mimeType = this.getTypeFromFormat(extension);

        if (!mimeType) return false;

        const canPlay = audio.canPlayType(mimeType);
        return canPlay !== '' && canPlay !== 'no';
    },

    /**
     * Check if a URL exists and is accessible
     * @param {string} url - The URL to check
     * @returns {Promise<boolean>} - Whether the URL exists and is accessible
     */
    async checkUrlExists(url) {
        try {
            const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' });
            return response.ok;
        } catch (error) {
            console.warn('Error checking URL existence:', error);
            return false;
        }
    },

    /**
     * Simplified URL existence check (synchronous version)
     * @param {string} url - The URL to check
     * @returns {boolean} - Whether the URL likely exists
     */
    urlExists(url) {
        // For simplicity and to avoid CORS issues, we'll make some assumptions
        if (!url) return false;

        // Check if it's a valid URL format
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    },

    /**
     * Convert YouTube video ID to various audio format URLs
     * @param {string} videoId - YouTube video ID
     * @returns {Object} - Object with URLs for different formats
     */
    getYouTubeAudioUrls(videoId) {
        return {
            proxy: `/api/youtube/audio-proxy?videoId=${videoId}`,
            direct: `/api/youtube/direct-audio/${videoId}`,
            fallback: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3'
        };
    },

    /**
     * Create a reliable audio element that should work in any browser
     * @param {Object} options - Configuration options
     * @returns {HTMLAudioElement} - Configured audio element
     */
    createReliableAudio(options = {}) {
        const defaults = {
            sources: [],
            fallbackUrl: 'https://cdn.pixabay.com/download/audio/2022/03/15/audio_c8e0386e38.mp3?filename=ambient-piano-amp-strings-10711.mp3',
            volume: 0.7,
            loop: false,
            autoplay: false,
            muted: false,
            preload: 'auto',
            onError: null,
            onSuccess: null
        };

        const config = { ...defaults, ...options };
        const audio = document.createElement('audio');

        // Set basic properties
        audio.volume = config.volume;
        audio.loop = config.loop;
        audio.autoplay = config.autoplay;
        audio.muted = config.muted;
        audio.preload = config.preload;

        // Add sources in order of browser preference
        const recommendedFormats = this.getRecommendedFormats();
        const sourcesToAdd = Array.isArray(config.sources) ? config.sources : [config.sources];

        // Add each source
        sourcesToAdd.forEach(sourceItem => {
            if (typeof sourceItem === 'string') {
                // It's a URL string
                const source = document.createElement('source');
                source.src = sourceItem;

                // Try to determine type from extension
                const extension = sourceItem.split('.').pop().toLowerCase();
                const mimeType = this.getTypeFromFormat(extension);
                if (mimeType) {
                    source.type = mimeType;
                }

                audio.appendChild(source);
            } else if (typeof sourceItem === 'object') {
                // It's an object with src and possibly type
                const source = document.createElement('source');
                source.src = sourceItem.src;
                if (sourceItem.type) {
                    source.type = sourceItem.type;
                }
                audio.appendChild(source);
            }
        });

        // Add error handling
        audio.addEventListener('error', (e) => {
            console.error('Audio error in createReliableAudio:', e);

            // Try fallback
            if (config.fallbackUrl) {
                console.log('Using fallback URL:', config.fallbackUrl);
                audio.src = config.fallbackUrl;
                audio.load();
            }

            // Call error callback if provided
            if (typeof config.onError === 'function') {
                config.onError(e);
            }
        });

        // Add success handler
        audio.addEventListener('canplaythrough', () => {
            console.log('Audio can play through without buffering');

            // Call success callback if provided
            if (typeof config.onSuccess === 'function') {
                config.onSuccess(audio);
            }
        });

        return audio;
    }
};

// Initialize and export the utilities
window.AudioUtils = AudioUtils.init();
