// Test script for YouTube functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testYouTubeSearch() {
    console.log('🎵 Testing YouTube Search API...\n');

    try {
        console.log('1. Searching for relaxing music...');
        const response = await axios.get(`${BASE_URL}/api/youtube/search`, {
            params: {
                query: 'relaxing music',
                limit: 5,
                filter: 'music'
            },
            timeout: 15000
        });

        if (response.data && response.data.items) {
            console.log('✅ YouTube search successful!');
            console.log(`📊 Found ${response.data.items.length} results`);
            
            // Show first few results
            response.data.items.slice(0, 3).forEach((item, index) => {
                console.log(`${index + 1}. ${item.title}`);
                console.log(`   Channel: ${item.channelTitle}`);
                console.log(`   Duration: ${item.duration || 'Unknown'}`);
                console.log(`   Video ID: ${item.id}`);
                console.log('');
            });

            return response.data.items[0]; // Return first result for further testing
        } else {
            console.log('❌ No search results found');
            return null;
        }
    } catch (error) {
        console.log('❌ YouTube search failed:', error.message);
        if (error.response) {
            console.log('Response status:', error.response.status);
            console.log('Response data:', error.response.data);
        }
        return null;
    }
}

async function testVideoIdExtraction() {
    console.log('\n🔗 Testing Video ID Extraction...\n');

    const testUrls = [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        'https://youtu.be/dQw4w9WgXcQ',
        'dQw4w9WgXcQ' // Direct video ID
    ];

    for (const url of testUrls) {
        try {
            console.log(`Testing URL: ${url}`);
            const response = await axios.get(`${BASE_URL}/api/youtube/extract-id`, {
                params: { url },
                timeout: 5000
            });

            console.log('✅ Extracted Video ID:', response.data.videoId);
        } catch (error) {
            console.log('❌ Failed to extract ID:', error.message);
        }
        console.log('');
    }
}

async function runYouTubeTests() {
    console.log('🚀 Starting YouTube API Tests...\n');
    
    // Test search and get a video for further testing
    const firstVideo = await testYouTubeSearch();
    
    await testVideoIdExtraction();
    
    console.log('\n✨ YouTube tests completed!');
}

runYouTubeTests().catch(console.error);
