// animations.js - JavaScript animations for StressAI

document.addEventListener('DOMContentLoaded', () => {
    // Initialize animations
    initPageTransitions();
    initButtonEffects();
    initParticleBackground();
    initScrollAnimations();
    initHoverEffects();
    initTypingEffects();
    initMusicVisualizer();
});

// Page transition animations
function initPageTransitions() {
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            // Add transition class to current page before hiding it
            const currentPage = document.querySelector('.page:not(.hidden)');
            if (currentPage) {
                currentPage.classList.add('page-transition-out');
                setTimeout(() => {
                    currentPage.classList.remove('page-transition-out');
                }, 500);
            }
        });
    });
}

// Button click effects
function initButtonEffects() {
    const buttons = document.querySelectorAll('button');

    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');

            // Position the ripple
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            this.appendChild(ripple);

            // Remove ripple after animation completes
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add CSS for ripple effect
    const style = document.createElement('style');
    style.textContent = `
        button {
            position: relative;
            overflow: hidden;
        }
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// Particle background effect
function initParticleBackground() {
    // Create canvas for particles
    const canvas = document.createElement('canvas');
    canvas.id = 'particle-canvas';
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '-1';
    canvas.style.opacity = '0.5';
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    let particles = [];

    // Resize canvas to window size
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    // Create particles
    function createParticles() {
        particles = [];
        const particleCount = Math.floor(window.innerWidth / 20); // Adjust density

        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                radius: Math.random() * 2 + 1,
                color: i % 2 === 0 ? '#3498db' : '#2ecc71',
                speed: Math.random() * 0.5 + 0.1,
                direction: Math.random() * Math.PI * 2
            });
        }
    }

    // Animate particles
    function animateParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        particles.forEach(particle => {
            // Move particle
            particle.x += Math.cos(particle.direction) * particle.speed;
            particle.y += Math.sin(particle.direction) * particle.speed;

            // Bounce off edges
            if (particle.x < 0 || particle.x > canvas.width) {
                particle.direction = Math.PI - particle.direction;
            }
            if (particle.y < 0 || particle.y > canvas.height) {
                particle.direction = -particle.direction;
            }

            // Draw particle
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
            ctx.fillStyle = particle.color;
            ctx.globalAlpha = 0.3;
            ctx.fill();
        });

        requestAnimationFrame(animateParticles);
    }

    // Initialize
    window.addEventListener('resize', () => {
        resizeCanvas();
        createParticles();
    });

    resizeCanvas();
    createParticles();
    animateParticles();
}

// Scroll animations
function initScrollAnimations() {
    // Add fade-in animation to elements as they scroll into view
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.activity-card, .input-group, .result-area, .history-section');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('scroll-visible');
            }
        });
    };

    // Add CSS for scroll animations (fade in without movement)
    const style = document.createElement('style');
    style.textContent = `
        .activity-card, .input-group, .result-area, .history-section {
            opacity: 0;
            transition: opacity 0.6s ease;
        }
        .scroll-visible {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);

    window.addEventListener('scroll', animateOnScroll);
    // Initial check
    setTimeout(animateOnScroll, 300);
}

// Hover effects (without floating)
function initHoverEffects() {
    // Add hover effect to activity cards (no vertical movement)
    const activityCards = document.querySelectorAll('.activity-card');

    activityCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            // Only apply scale and shadow, no vertical movement
            card.style.transform = 'scale(1.02)';
            card.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.1)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
            card.style.boxShadow = '';
        });
    });
}

// Typing effects for chat
function initTypingEffects() {
    const userMessageInput = document.getElementById('userMessageInput');

    if (userMessageInput) {
        userMessageInput.addEventListener('focus', () => {
            userMessageInput.classList.add('typing');
        });

        userMessageInput.addEventListener('blur', () => {
            userMessageInput.classList.remove('typing');
        });

        // Add CSS for typing animation
        const style = document.createElement('style');
        style.textContent = `
            .typing {
                background-color: rgba(52, 152, 219, 0.1) !important;
            }
        `;
        document.head.appendChild(style);
    }
}

// Music visualizer
function initMusicVisualizer() {
    const playAiMusicBtn = document.getElementById('playAiMusicBtn');

    if (playAiMusicBtn) {
        // Create visualizer container
        const visualizer = document.createElement('div');
        visualizer.className = 'music-visualizer';
        visualizer.style.display = 'none';

        // Create bars
        for (let i = 0; i < 10; i++) {
            const bar = document.createElement('div');
            bar.className = 'visualizer-bar';
            visualizer.appendChild(bar);
        }

        // Add visualizer to player
        const aiTrackPlayer = document.querySelector('.ai-track-player');
        if (aiTrackPlayer) {
            aiTrackPlayer.appendChild(visualizer);
        }

        // Toggle visualizer when playing
        playAiMusicBtn.addEventListener('click', () => {
            if (playAiMusicBtn.classList.contains('playing')) {
                visualizer.style.display = 'flex';
                animateVisualizer();
            } else {
                visualizer.style.display = 'none';
            }
        });

        // Add CSS for visualizer
        const style = document.createElement('style');
        style.textContent = `
            .music-visualizer {
                display: flex;
                justify-content: center;
                align-items: flex-end;
                height: 40px;
                gap: 3px;
                margin-top: 10px;
            }
            .visualizer-bar {
                width: 4px;
                background-color: #2ecc71;
                border-radius: 2px;
                transition: height 0.2s ease;
            }
        `;
        document.head.appendChild(style);

        // Animate visualizer bars
        function animateVisualizer() {
            if (visualizer.style.display === 'none') return;

            const bars = visualizer.querySelectorAll('.visualizer-bar');
            bars.forEach(bar => {
                const height = Math.floor(Math.random() * 30) + 5;
                bar.style.height = `${height}px`;
            });

            setTimeout(animateVisualizer, 200);
        }
    }
}
