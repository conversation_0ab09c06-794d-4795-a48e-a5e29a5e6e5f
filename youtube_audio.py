#!/usr/bin/env python3
"""
YouTube Audio Extractor and Validator

This script provides reliable YouTube audio extraction and validation.
It can be used as a standalone script or called from Node.js.
"""

import sys
import os
import json
import argparse
import tempfile
import logging
from urllib.parse import urlparse, parse_qs
import requests
import subprocess
from typing import Dict, List, Optional, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('youtube_audio')

# Check if yt-dlp is installed, otherwise use pytube as fallback
try:
    import yt_dlp
    USE_YTDLP = True
    logger.info("Using yt-dlp for YouTube extraction")
except ImportError:
    USE_YTDLP = False
    try:
        from pytube import YouTube
        logger.info("Using pytube for YouTube extraction")
    except ImportError:
        logger.error("Neither yt-dlp nor pytube is installed. Please install one of them.")
        logger.error("pip install yt-dlp  # Recommended")
        logger.error("or")
        logger.error("pip install pytube")
        sys.exit(1)

# Try to import FFmpeg Python bindings if available
try:
    import ffmpeg
    HAS_FFMPEG_PYTHON = True
except ImportError:
    HAS_FFMPEG_PYTHON = False
    logger.warning("ffmpeg-python not installed. Some features will be limited.")
    logger.warning("Install with: pip install ffmpeg-python")

def extract_video_id(url: str) -> Optional[str]:
    """Extract the YouTube video ID from various URL formats."""
    if not url:
        return None

    # Check if it's already a video ID (11 characters)
    if len(url) == 11 and all(c in 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-' for c in url):
        return url

    # Parse the URL
    parsed_url = urlparse(url)

    # youtube.com/watch?v=VIDEO_ID
    if parsed_url.netloc in ('youtube.com', 'www.youtube.com') and parsed_url.path == '/watch':
        query = parse_qs(parsed_url.query)
        return query.get('v', [None])[0]

    # youtu.be/VIDEO_ID
    if parsed_url.netloc == 'youtu.be':
        return parsed_url.path.lstrip('/')

    # youtube.com/embed/VIDEO_ID
    if parsed_url.netloc in ('youtube.com', 'www.youtube.com') and parsed_url.path.startswith('/embed/'):
        return parsed_url.path.split('/')[2]

    # youtube.com/v/VIDEO_ID
    if parsed_url.netloc in ('youtube.com', 'www.youtube.com') and parsed_url.path.startswith('/v/'):
        return parsed_url.path.split('/')[2]

    # youtube.com/shorts/VIDEO_ID
    if parsed_url.netloc in ('youtube.com', 'www.youtube.com') and parsed_url.path.startswith('/shorts/'):
        return parsed_url.path.split('/')[2]

    return None

def get_audio_info_ytdlp(video_id: str) -> Dict:
    """Get audio information using yt-dlp."""
    url = f"https://www.youtube.com/watch?v={video_id}"

    ydl_opts = {
        'format': 'bestaudio/best',
        'quiet': True,
        'no_warnings': True,
        'extract_flat': True,
        'skip_download': True,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            # Extract relevant information
            result = {
                'videoId': video_id,
                'title': info.get('title', 'Unknown Title'),
                'author': info.get('uploader', 'Unknown Author'),
                'lengthSeconds': info.get('duration', 0),
                'formats': []
            }

            # Extract format information
            for format_info in info.get('formats', []):
                if format_info.get('acodec') != 'none':  # Only audio formats
                    result['formats'].append({
                        'itag': format_info.get('format_id'),
                        'mimeType': format_info.get('ext', 'mp4'),
                        'audioBitrate': format_info.get('abr', 0),
                        'url': format_info.get('url'),
                        'filesize': format_info.get('filesize'),
                    })

            return result
    except Exception as e:
        logger.error(f"Error extracting info with yt-dlp: {e}")
        return {'error': str(e)}

def get_audio_info_pytube(video_id: str) -> Dict:
    """Get audio information using pytube."""
    url = f"https://www.youtube.com/watch?v={video_id}"

    try:
        yt = YouTube(url)

        # Extract relevant information
        result = {
            'videoId': video_id,
            'title': yt.title,
            'author': yt.author,
            'lengthSeconds': yt.length,
            'formats': []
        }

        # Extract format information
        for stream in yt.streams.filter(only_audio=True):
            result['formats'].append({
                'itag': stream.itag,
                'mimeType': stream.mime_type,
                'audioBitrate': stream.abr.replace('kbps', '') if stream.abr else 0,
                'url': stream.url,
                'filesize': stream.filesize,
            })

        return result
    except Exception as e:
        logger.error(f"Error extracting info with pytube: {e}")
        return {'error': str(e)}

def download_audio(video_id: str, output_dir: Optional[str] = None, format: str = 'mp3') -> Optional[str]:
    """Download audio from YouTube and convert to specified format."""
    if not output_dir:
        output_dir = tempfile.gettempdir()

    output_file = os.path.join(output_dir, f"{video_id}.{format}")

    if USE_YTDLP:
        return download_audio_ytdlp(video_id, output_file, format)
    else:
        return download_audio_pytube(video_id, output_file, format)

def download_audio_ytdlp(video_id: str, output_file: str, format: str) -> Optional[str]:
    """Download audio using yt-dlp."""
    url = f"https://www.youtube.com/watch?v={video_id}"

    ydl_opts = {
        'format': 'bestaudio/best',
        'outtmpl': output_file.replace(f".{format}", ""),
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': format,
            'preferredquality': '192',
        }],
        'quiet': False,
        'no_warnings': True,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])

        # yt-dlp adds the extension automatically
        final_output = f"{output_file.replace(f'.{format}', '')}.{format}"

        if os.path.exists(final_output):
            return final_output
        return None
    except Exception as e:
        logger.error(f"Error downloading with yt-dlp: {e}")
        return None

def download_audio_pytube(video_id: str, output_file: str, format: str) -> Optional[str]:
    """Download audio using pytube and convert with FFmpeg."""
    url = f"https://www.youtube.com/watch?v={video_id}"

    try:
        yt = YouTube(url)
        stream = yt.streams.filter(only_audio=True).order_by('abr').desc().first()

        if not stream:
            logger.error("No audio stream found")
            return None

        # Download the audio
        temp_file = stream.download(output_path=os.path.dirname(output_file),
                                   filename=os.path.basename(output_file).replace(f".{format}", ".tmp"))

        # Convert to desired format using FFmpeg
        try:
            subprocess.run([
                'ffmpeg', '-i', temp_file, '-vn', '-ar', '44100', '-ac', '2', '-b:a', '192k',
                '-f', format, output_file, '-y'
            ], check=True, capture_output=True)

            # Remove temporary file
            os.remove(temp_file)

            if os.path.exists(output_file):
                return output_file
        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except Exception as e:
            logger.error(f"Error converting audio: {e}")

        return None
    except Exception as e:
        logger.error(f"Error downloading with pytube: {e}")
        return None

def validate_audio_url(url: str) -> Dict:
    """Validate if an audio URL is accessible and playable."""
    try:
        # Try to get headers only to check if URL is valid
        response = requests.head(url, timeout=5)

        if response.status_code == 200:
            content_type = response.headers.get('Content-Type', '')
            content_length = response.headers.get('Content-Length', '0')

            return {
                'valid': True,
                'status_code': response.status_code,
                'content_type': content_type,
                'content_length': content_length,
                'url': url
            }
        else:
            return {
                'valid': False,
                'status_code': response.status_code,
                'url': url
            }
    except Exception as e:
        return {
            'valid': False,
            'error': str(e),
            'url': url
        }

def get_direct_stream_url(video_id: str) -> Dict:
    """Get a direct streaming URL for the video."""
    try:
        if USE_YTDLP:
            info = get_audio_info_ytdlp(video_id)
        else:
            info = get_audio_info_pytube(video_id)

        if 'error' in info:
            return {'success': False, 'error': info['error']}

        # Find the best audio format
        best_format = None
        for format in info.get('formats', []):
            if not best_format or (format.get('audioBitrate', 0) > best_format.get('audioBitrate', 0)):
                best_format = format

        if best_format and 'url' in best_format:
            # Validate the URL
            validation = validate_audio_url(best_format['url'])
            if validation.get('valid', False):
                return {
                    'success': True,
                    'url': best_format['url'],
                    'mimeType': best_format.get('mimeType', 'audio/mp4'),
                    'title': info.get('title', 'Unknown Title'),
                    'author': info.get('author', 'Unknown Author')
                }

        return {'success': False, 'error': 'No valid audio URL found'}
    except Exception as e:
        logger.error(f"Error getting direct stream URL: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """Main function to handle command-line arguments."""
    parser = argparse.ArgumentParser(description='YouTube Audio Extractor and Validator')
    parser.add_argument('--url', help='YouTube URL or video ID')
    parser.add_argument('--info', action='store_true', help='Get audio information')
    parser.add_argument('--download', action='store_true', help='Download audio')
    parser.add_argument('--validate', action='store_true', help='Validate audio URL')
    parser.add_argument('--stream', action='store_true', help='Get direct stream URL')
    parser.add_argument('--format', default='mp3', help='Audio format (mp3, m4a, etc.)')
    parser.add_argument('--output', help='Output directory or file')

    args = parser.parse_args()

    if not args.url:
        parser.print_help()
        return

    # Extract video ID
    video_id = extract_video_id(args.url)
    if not video_id:
        print(json.dumps({'error': 'Invalid YouTube URL or video ID'}))
        return

    # Get audio information
    if args.info:
        if USE_YTDLP:
            info = get_audio_info_ytdlp(video_id)
        else:
            info = get_audio_info_pytube(video_id)
        print(json.dumps(info))

    # Get direct stream URL
    elif args.stream:
        result = get_direct_stream_url(video_id)
        print(json.dumps(result))

    # Download audio
    elif args.download:
        output_file = download_audio(video_id, args.output, args.format)
        if output_file:
            print(json.dumps({'success': True, 'file': output_file}))
        else:
            print(json.dumps({'success': False, 'error': 'Failed to download audio'}))

    # Validate audio URL
    elif args.validate:
        if USE_YTDLP:
            info = get_audio_info_ytdlp(video_id)
        else:
            info = get_audio_info_pytube(video_id)

        if 'error' in info:
            print(json.dumps({'valid': False, 'error': info['error']}))
            return

        # Validate the first audio URL
        if info['formats']:
            result = validate_audio_url(info['formats'][0]['url'])
            print(json.dumps(result))
        else:
            print(json.dumps({'valid': False, 'error': 'No audio formats found'}))

if __name__ == '__main__':
    main()
