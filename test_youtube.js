#!/usr/bin/env node
/**
 * Test script to verify YouTube audio extraction functionality
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testYouTubeAudio() {
    console.log('🎵 Testing YouTube Audio Extraction...\n');

    // Test video IDs - using popular relaxing music videos
    const testVideos = [
        'jfKfPfyJRdk', // Relaxing music video
        'lFcSrYw-ARY', // Another relaxing music video
        'invalid123'   // Invalid video ID for error testing
    ];

    for (const videoId of testVideos) {
        console.log(`\n📹 Testing video ID: ${videoId}`);
        console.log('='.repeat(50));

        try {
            // Test video info endpoint
            console.log('🔍 Testing video info extraction...');
            const infoResponse = await axios.get(`${BASE_URL}/api/youtube/video-info/${videoId}`);

            if (infoResponse.status === 200) {
                const info = infoResponse.data;
                console.log(`✅ Video info extracted successfully:`);
                console.log(`   Title: ${info.title}`);
                console.log(`   Author: ${info.author}`);
                console.log(`   Duration: ${info.lengthSeconds} seconds`);
                console.log(`   Audio formats found: ${info.formats.length}`);

                if (info.formats.length > 0) {
                    console.log(`   Best format: ${info.formats[0].mimeType} (${info.formats[0].audioBitrate}kbps)`);
                }
            }

            // Test streaming endpoint
            console.log('🎵 Testing audio streaming...');
            const streamResponse = await axios.head(`${BASE_URL}/api/youtube/stream/${videoId}`);

            if (streamResponse.status === 200) {
                console.log(`✅ Audio streaming endpoint accessible`);
                console.log(`   Content-Type: ${streamResponse.headers['content-type']}`);
            }

        } catch (error) {
            if (error.response) {
                console.log(`❌ Error ${error.response.status}: ${error.response.data.error}`);
                if (error.response.data.suggestion) {
                    console.log(`💡 Suggestion: ${error.response.data.suggestion}`);
                }
            } else {
                console.log(`❌ Network error: ${error.message}`);
            }
        }
    }

    console.log('\n🔍 Testing URL validation...');
    const testUrls = [
        'https://www.youtube.com/watch?v=jfKfPfyJRdk',
        'https://youtu.be/lFcSrYw-ARY',
        'invalid-url'
    ];

    for (const url of testUrls) {
        try {
            console.log(`\n🔗 Testing URL: ${url}`);
            const validateResponse = await axios.get(`${BASE_URL}/api/youtube/validate?url=${encodeURIComponent(url)}`);

            if (validateResponse.data.valid) {
                console.log(`✅ URL is valid - Video ID: ${validateResponse.data.videoId}`);
            } else {
                console.log(`❌ URL is invalid: ${validateResponse.data.error}`);
            }
        } catch (error) {
            console.log(`❌ Validation error: ${error.message}`);
        }
    }

    console.log('\n🎯 Testing complete!');
    console.log('\n📝 Summary:');
    console.log('- If you see "Could not extract functions" errors, this is expected when YouTube updates');
    console.log('- The app will automatically fall back to built-in relaxation music');
    console.log('- For better reliability, install Python dependencies: python install_python_deps.py');
}

// Run the test
if (require.main === module) {
    testYouTubeAudio().catch(console.error);
}

module.exports = { testYouTubeAudio };
