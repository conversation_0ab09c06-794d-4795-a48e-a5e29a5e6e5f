#!/usr/bin/env node
/**
 * Test script to verify dual model functionality (OpenRouter + Ollama)
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testModels() {
    console.log('🤖 Testing Dual Model Functionality...\n');
    
    try {
        // Test 1: Check model status
        console.log('📊 Checking model status...');
        const statusResponse = await axios.get(`${BASE_URL}/api/model/status`);
        const status = statusResponse.data;
        
        console.log(`Current preference: ${status.current_preference}`);
        console.log(`Online model (${status.online_model.name}): ${status.online_model.status}`);
        console.log(`Offline model (${status.offline_model.name}): ${status.offline_model.status}`);
        
        // Test 2: Test online model (if available)
        if (status.online_model.status === 'available') {
            console.log('\n🌐 Testing online model (DeepSeek)...');
            try {
                const onlineResponse = await axios.post(`${BASE_URL}/api/chat/completions`, {
                    messages: [
                        {
                            role: 'user',
                            content: 'Hello! Please respond with exactly "Online model working" to confirm you are the DeepSeek model.'
                        }
                    ],
                    modelPreference: 'online'
                });
                
                console.log(`✅ Online response: ${onlineResponse.data.content}`);
                console.log(`   Model used: ${onlineResponse.data.model_used}`);
                console.log(`   Model name: ${onlineResponse.data.model_name}`);
            } catch (error) {
                console.log(`❌ Online model error: ${error.message}`);
            }
        } else {
            console.log('\n🌐 Online model not available');
        }
        
        // Test 3: Test offline model (if available)
        if (status.offline_model.status === 'available') {
            console.log('\n💻 Testing offline model (Llama)...');
            try {
                const offlineResponse = await axios.post(`${BASE_URL}/api/chat/completions`, {
                    messages: [
                        {
                            role: 'user',
                            content: 'Hello! Please respond with exactly "Offline model working" to confirm you are the Llama model.'
                        }
                    ],
                    modelPreference: 'offline'
                });
                
                console.log(`✅ Offline response: ${offlineResponse.data.content}`);
                console.log(`   Model used: ${offlineResponse.data.model_used}`);
                console.log(`   Model name: ${offlineResponse.data.model_name}`);
            } catch (error) {
                console.log(`❌ Offline model error: ${error.message}`);
            }
        } else {
            console.log('\n💻 Offline model not available');
        }
        
        // Test 4: Test auto mode
        console.log('\n🔄 Testing auto mode (best available)...');
        try {
            const autoResponse = await axios.post(`${BASE_URL}/api/chat/completions`, {
                messages: [
                    {
                        role: 'user',
                        content: 'What is 2+2? Please give a brief answer.'
                    }
                ],
                modelPreference: 'auto'
            });
            
            console.log(`✅ Auto response: ${autoResponse.data.content}`);
            console.log(`   Model used: ${autoResponse.data.model_used}`);
            console.log(`   Model name: ${autoResponse.data.model_name}`);
        } catch (error) {
            console.log(`❌ Auto mode error: ${error.message}`);
        }
        
        // Test 5: Test preference switching
        console.log('\n⚙️ Testing preference switching...');
        const preferences = ['online', 'offline', 'auto'];
        
        for (const pref of preferences) {
            try {
                const switchResponse = await axios.post(`${BASE_URL}/api/model/preference`, {
                    preference: pref
                });
                
                if (switchResponse.data.success) {
                    console.log(`✅ Successfully switched to ${pref} mode`);
                } else {
                    console.log(`❌ Failed to switch to ${pref} mode`);
                }
            } catch (error) {
                console.log(`❌ Error switching to ${pref}: ${error.message}`);
            }
        }
        
        console.log('\n🎯 Model testing complete!');
        console.log('\n📝 Summary:');
        console.log('- Your stress AI now supports both online (DeepSeek) and offline (Llama) models');
        console.log('- Auto mode will use the best available model');
        console.log('- You can switch between models in the AI Chat interface');
        console.log('- Online model provides more advanced responses');
        console.log('- Offline model works without internet connection');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Make sure the server is running: node server.js');
        }
    }
}

// Run the test
if (require.main === module) {
    testModels().catch(console.error);
}

module.exports = { testModels };
