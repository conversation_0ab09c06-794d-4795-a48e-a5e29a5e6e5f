#!/usr/bin/env node
/**
 * Test script to verify stress prediction functionality
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testStressPrediction() {
    console.log('🧠 Testing Stress Prediction Functionality...\n');
    
    // Test data representing a stressed student
    const testData = {
        messages: [
            {
                role: "system",
                content: `You are a helpful AI assistant that predicts a student's daily stress level based on several factors. You also provide a brief, positive piece of advice and personalized tips. Be encouraging, concise, and provide actionable advice. Use a friendly, supportive tone.
Respond ONLY with a JSON object matching this schema:
{
  "stressLevel": "string (Low, Moderate, High, or Very High)",
  "stressScore": "number between 0 and 100, with 0 being no stress and 100 being maximum stress",
  "advice": "string (1-2 concise sentences of positive advice)",
  "emoji": "string (a single emoji representing the stress level or mood)",
  "tips": ["array of 3 brief, specific tips based on their inputs"]
}
Do not include any other text or explanations outside of the JSON object. Keep advice encouraging and actionable if possible.`
            },
            {
                role: "user",
                content: `Predict the stress level for a student with the following daily factors:
- Sleep: 5 hours
- Homework Load (0-10 scale): 8
- Number of Upcoming Exams: 3
- Social Interaction (0-5 scale): 2
- Extracurricular Activities (0-5 scale): 4
- Nutrition Quality (0-5 scale): 2
- Screen Time: 8 hours

Consider these factors together to determine stress level. For example, low sleep, high homework load, and many exams would indicate high stress. Good social interaction and nutrition might lower stress levels.`
            }
        ],
        modelPreference: 'auto'
    };
    
    try {
        console.log('📊 Sending stress prediction request...');
        console.log('Test scenario: High stress student (low sleep, high workload, many exams)');
        
        const response = await axios.post(`${BASE_URL}/api/chat/completions`, testData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 second timeout
        });
        
        if (response.status === 200) {
            const result = response.data;
            
            console.log('\n✅ Stress prediction successful!');
            console.log(`Model used: ${result.model_used || 'unknown'}`);
            console.log(`Model name: ${result.model_name || 'unknown'}`);
            
            // Try to parse the response content
            try {
                let prediction;
                
                // Check if content is already parsed JSON
                if (typeof result.content === 'object') {
                    prediction = result.content;
                } else {
                    // Try to extract JSON from the content
                    const jsonMatch = result.content.match(/\{[\s\S]*\}/);
                    const jsonString = jsonMatch ? jsonMatch[0] : result.content;
                    prediction = JSON.parse(jsonString);
                }
                
                console.log('\n📋 Prediction Results:');
                console.log(`Stress Level: ${prediction.stressLevel || 'N/A'}`);
                console.log(`Stress Score: ${prediction.stressScore || 'N/A'}/100`);
                console.log(`Emoji: ${prediction.emoji || 'N/A'}`);
                console.log(`Advice: ${prediction.advice || 'N/A'}`);
                
                if (prediction.tips && prediction.tips.length > 0) {
                    console.log('\n💡 Personalized Tips:');
                    prediction.tips.forEach((tip, index) => {
                        console.log(`  ${index + 1}. ${tip}`);
                    });
                }
                
                // Validate the prediction makes sense
                const stressScore = prediction.stressScore || 0;
                if (stressScore > 60) {
                    console.log('\n✅ Prediction seems accurate - high stress detected for stressed student scenario');
                } else {
                    console.log('\n⚠️  Prediction might be low - expected higher stress for this scenario');
                }
                
            } catch (parseError) {
                console.log('\n⚠️  Could not parse prediction JSON, but got response:');
                console.log(result.content);
            }
            
        } else {
            console.log(`❌ Unexpected response status: ${response.status}`);
        }
        
    } catch (error) {
        console.error('\n❌ Stress prediction test failed:');
        
        if (error.response) {
            console.log(`HTTP Status: ${error.response.status}`);
            console.log(`Error: ${error.response.data.error || 'Unknown error'}`);
            if (error.response.data.suggestion) {
                console.log(`Suggestion: ${error.response.data.suggestion}`);
            }
        } else if (error.code === 'ECONNREFUSED') {
            console.log('Connection refused - make sure the server is running on port 3000');
            console.log('Start the server with: npm start');
        } else {
            console.log(`Error: ${error.message}`);
        }
    }
    
    console.log('\n🎯 Test complete!');
    console.log('\nTo test manually:');
    console.log('1. Open http://localhost:3000');
    console.log('2. Go to "Stress Predictor" section');
    console.log('3. Fill in the form and click "Predict My Stress Level"');
}

// Run the test
if (require.main === module) {
    testStressPrediction().catch(console.error);
}

module.exports = { testStressPrediction };
