/* animations-advanced.css - More sophisticated animations for StressAI */

/* ===== ADVANCED ANIMATIONS ===== */

/* Particle background effect */
body {
    position: relative;
}

body:after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(52, 152, 219, 0.03) 0%, transparent 8%),
        radial-gradient(circle at 50% 80%, rgba(46, 204, 113, 0.03) 0%, transparent 8%),
        radial-gradient(circle at 80% 20%, rgba(52, 152, 219, 0.03) 0%, transparent 8%),
        radial-gradient(circle at 10% 60%, rgba(46, 204, 113, 0.03) 0%, transparent 8%),
        radial-gradient(circle at 90% 90%, rgba(52, 152, 219, 0.03) 0%, transparent 8%);
    background-size: 120% 120%;
    z-index: -1;
    opacity: 0.7;
    animation: particleShift 20s ease-in-out infinite alternate;
}

@keyframes particleShift {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

/* Animated gradient borders */
.container {
    position: relative;
}

.container:before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    z-index: -1;
    background: linear-gradient(45deg, #3498db, #2ecc71, #3498db);
    background-size: 400% 400%;
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: gradientBorder 3s ease infinite;
}

.container:hover:before {
    opacity: 0.7;
}

@keyframes gradientBorder {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Animated icons for activity cards */
.activity-card h3 {
    position: relative;
    display: inline-block;
}

.activity-card h3:before {
    content: '✨';
    position: absolute;
    left: -25px;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
}

.activity-card:hover h3:before {
    opacity: 1;
    transform: translateX(0);
}

/* Animated progress bars */
.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
    transform: translateX(-100%);
}

.progress-bar:hover:after {
    animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
    100% { transform: translateX(100%); }
}

/* Animated breathing instructions */
#breathingInstruction {
    transition: all 0.5s ease;
    position: relative;
}

#breathingInstruction:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: #3498db;
    transform: translateX(-50%);
    transition: width 0.5s ease;
}

#breathingInstruction:hover:after {
    width: 50%;
}

/* Enhanced button hover effects (without floating) */
.activity-btn, #predictButton, .save-button, .play-btn {
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.activity-btn:hover, #predictButton:hover, .save-button:hover, .play-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.activity-btn:active, #predictButton:active, .save-button:active, .play-btn:active {
    transform: scale(0.98);
}

/* Animated form inputs */
.input-group input {
    transition: all 0.3s ease;
}

.input-group input:focus {
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.input-group span {
    transition: all 0.3s ease;
}

.input-group input:focus + span {
    color: #2980b9;
    transform: scale(1.1);
}

/* Animated chat messages (without floating) */
.message {
    transition: all 0.3s ease;
}

.message:hover {
    background-color: rgba(52, 152, 219, 0.03);
}

.message-bubble {
    position: relative;
    overflow: hidden;
}

.message-bubble:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.1),
                transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message:hover .message-bubble:after {
    opacity: 1;
}

/* Animated profile avatar */
#profileAvatarPreview {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#profileAvatarPreview:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Animated stress meter */
.stress-meter {
    position: relative;
    overflow: hidden;
}

.stress-meter:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
    z-index: 1;
    animation: stressMeterShine 2s infinite;
    opacity: 0.5;
}

@keyframes stressMeterShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Animated music player */
.ai-player-controls {
    position: relative;
}

.play-btn {
    position: relative;
    z-index: 1;
}

.play-btn:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    transition: all 0.5s ease;
}

.play-btn:hover:before {
    width: 150%;
    height: 150%;
}

/* Animated volume control */
.ai-volume-control {
    position: relative;
}

.ai-volume-control:after {
    content: '🔊';
    position: absolute;
    right: -25px;
    top: 50%;
    transform: translateY(-50%) scale(0);
    transition: all 0.3s ease;
    opacity: 0;
}

.ai-volume-control:hover:after {
    transform: translateY(-50%) scale(1);
    opacity: 0.7;
}

/* Animated modal transitions */
.modal-content {
    animation: modalSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Animated close button */
.close-modal {
    transition: all 0.3s ease;
}

.close-modal:hover {
    transform: rotate(90deg);
    color: #e74c3c;
}

/* Animated suggestion chips (without floating) */
.suggestion-chip {
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.suggestion-chip:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.2);
}

/* Animated chart appearance */
.chart-container {
    opacity: 0;
    transform: translateY(20px);
    animation: chartAppear 0.8s ease forwards;
    animation-delay: 0.3s;
}

@keyframes chartAppear {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animated history items (without floating) */
.history-item {
    transition: all 0.3s ease;
}

.history-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
}
