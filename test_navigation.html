<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .nav-test { margin: 20px 0; }
        .nav-test button { 
            margin: 5px; 
            padding: 10px 20px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .nav-test button:hover { background: #0056b3; }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px; 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>🧪 Navigation Test for Stress AI</h1>
    
    <div class="nav-test">
        <h3>Test Navigation Elements:</h3>
        <button onclick="testNavigation()">🔍 Test Navigation</button>
        <button onclick="testPageElements()">📄 Test Page Elements</button>
        <button onclick="simulateNavClick('main')">📊 Go to Predictor</button>
        <button onclick="simulateNavClick('activities')">🎯 Go to Activities</button>
        <button onclick="simulateNavClick('ai-chat')">🤖 Go to AI Chat</button>
    </div>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function testNavigation() {
            log('🔍 Testing navigation elements...', 'info');
            
            // Test if we can access the main window
            try {
                if (window.opener || window.parent !== window) {
                    log('❌ Cannot test navigation - not in main window', 'error');
                    return;
                }
                
                // Check if nav elements exist
                const navItems = document.querySelectorAll('.nav-item');
                if (navItems.length === 0) {
                    log('❌ No navigation items found. Opening main application...', 'error');
                    window.open('http://localhost:3000', '_blank');
                    return;
                }
                
                log(`✅ Found ${navItems.length} navigation items`, 'success');
                
                // Test page elements
                testPageElements();
                
            } catch (error) {
                log(`❌ Error testing navigation: ${error.message}`, 'error');
                log('🔗 Opening main application in new tab...', 'info');
                window.open('http://localhost:3000', '_blank');
            }
        }

        function testPageElements() {
            log('📄 Testing page elements...', 'info');
            
            const pages = ['mainPage', 'activitiesPage', 'aiChatPage'];
            let foundPages = 0;
            
            pages.forEach(pageId => {
                const element = document.getElementById(pageId);
                if (element) {
                    foundPages++;
                    log(`✅ Found ${pageId}`, 'success');
                } else {
                    log(`❌ Missing ${pageId}`, 'error');
                }
            });
            
            if (foundPages === pages.length) {
                log('🎉 All page elements found! Navigation should work.', 'success');
            } else {
                log(`⚠️ Only ${foundPages}/${pages.length} page elements found.`, 'error');
            }
        }

        function simulateNavClick(targetPage) {
            log(`🔄 Simulating navigation to ${targetPage}...`, 'info');
            
            try {
                // Try to find and click the navigation item
                const navItems = document.querySelectorAll('.nav-item');
                let found = false;
                
                navItems.forEach(item => {
                    if (item.getAttribute('data-page') === targetPage) {
                        item.click();
                        found = true;
                        log(`✅ Clicked navigation to ${targetPage}`, 'success');
                    }
                });
                
                if (!found) {
                    log(`❌ Navigation item for ${targetPage} not found`, 'error');
                    log('🔗 Opening main application...', 'info');
                    window.open('http://localhost:3000', '_blank');
                }
                
            } catch (error) {
                log(`❌ Error simulating navigation: ${error.message}`, 'error');
                log('🔗 Opening main application...', 'info');
                window.open('http://localhost:3000', '_blank');
            }
        }

        // Auto-test on load
        window.addEventListener('load', () => {
            log('🚀 Navigation test page loaded', 'info');
            log('💡 Click the buttons above to test navigation, or click below to open the main app:', 'info');
            
            // Add a direct link to the main app
            const link = document.createElement('div');
            link.innerHTML = `
                <div style="margin: 20px 0; padding: 15px; background: #e3f2fd; border-radius: 5px;">
                    <strong>🔗 Direct Link:</strong> 
                    <a href="http://localhost:3000" target="_blank" style="color: #1976d2; text-decoration: none;">
                        Open Stress AI Application
                    </a>
                </div>
            `;
            document.getElementById('results').appendChild(link);
        });
    </script>
</body>
</html>
