// Test script for stress prediction API
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testStressPrediction() {
    console.log('🧪 Testing Stress Prediction API...\n');

    // Test data for stress prediction
    const testCases = [
        {
            name: 'Low Stress Case',
            data: {
                sleep: 8,
                studyHours: 4,
                exercise: 1,
                socialTime: 2,
                screenTime: 3,
                mood: 'happy'
            }
        },
        {
            name: 'High Stress Case',
            data: {
                sleep: 4,
                studyHours: 10,
                exercise: 0,
                socialTime: 0,
                screenTime: 8,
                mood: 'anxious'
            }
        },
        {
            name: 'Moderate Stress Case',
            data: {
                sleep: 6,
                studyHours: 6,
                exercise: 0.5,
                socialTime: 1,
                screenTime: 5,
                mood: 'neutral'
            }
        }
    ];

    for (const testCase of testCases) {
        console.log(`\n📊 Testing: ${testCase.name}`);
        console.log('Input data:', testCase.data);

        try {
            const response = await axios.post(`${BASE_URL}/api/stress/predict`, {
                studentData: testCase.data
            }, {
                timeout: 30000 // 30 second timeout
            });

            if (response.data.success) {
                console.log('✅ Prediction successful!');
                console.log('📈 Stress Level:', response.data.prediction.stressLevel);
                console.log('📊 Stress Score:', response.data.prediction.stressScore);
                console.log('🔍 Source:', response.data.source);
                if (response.data.model_used) {
                    console.log('🤖 Model Used:', response.data.model_used);
                }
                console.log('💡 Advice:', response.data.prediction.advice.slice(0, 2).join(', '));
            } else {
                console.log('❌ Prediction failed:', response.data.error);
            }
        } catch (error) {
            console.log('❌ Request failed:', error.message);
            if (error.response) {
                console.log('Response status:', error.response.status);
                console.log('Response data:', error.response.data);
            }
        }
    }
}

async function testModelStatus() {
    console.log('\n🔍 Testing Model Status API...\n');

    try {
        const response = await axios.get(`${BASE_URL}/api/model/status`, {
            timeout: 15000
        });

        console.log('✅ Model status retrieved successfully!');
        console.log('🌐 Online Model (DeepSeek):', response.data.online.available ? '✅ Available' : '❌ Unavailable');
        if (response.data.online.error) {
            console.log('   Error:', response.data.online.error);
        }
        
        console.log('💻 Offline Model (Llama):', response.data.offline.available ? '✅ Available' : '❌ Unavailable');
        if (response.data.offline.error) {
            console.log('   Error:', response.data.offline.error);
        }
        
        console.log('⚙️ Current Preference:', response.data.current_preference);

    } catch (error) {
        console.log('❌ Model status check failed:', error.message);
        if (error.response) {
            console.log('Response status:', error.response.status);
            console.log('Response data:', error.response.data);
        }
    }
}

async function testChatCompletion() {
    console.log('\n💬 Testing AI Chat Completion...\n');

    const testMessage = {
        messages: [
            {
                role: 'user',
                content: 'I am feeling stressed about my upcoming exams. Can you give me some quick advice?'
            }
        ],
        stressData: {
            sleep: 5,
            studyHours: 8,
            exercise: 0,
            socialTime: 1,
            screenTime: 6,
            stressLevel: 7,
            mood: 'anxious'
        }
    };

    try {
        const response = await axios.post(`${BASE_URL}/api/chat/completions`, testMessage, {
            timeout: 30000
        });

        if (response.data.success) {
            console.log('✅ Chat completion successful!');
            console.log('🤖 Model Used:', response.data.model_used);
            console.log('💬 Response:', response.data.data.choices[0].message.content.substring(0, 200) + '...');
        } else {
            console.log('❌ Chat completion failed:', response.data.error);
        }
    } catch (error) {
        console.log('❌ Chat completion request failed:', error.message);
        if (error.response) {
            console.log('Response status:', error.response.status);
            console.log('Response data:', error.response.data);
        }
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Starting Stress AI API Tests...\n');
    
    await testModelStatus();
    await testStressPrediction();
    await testChatCompletion();
    
    console.log('\n✨ All tests completed!');
}

runAllTests().catch(console.error);
