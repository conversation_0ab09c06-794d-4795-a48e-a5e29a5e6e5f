// volume-control.js - Enhanced volume control functionality

document.addEventListener('DOMContentLoaded', () => {
    initVolumeControl();
});

function initVolumeControl() {
    const volumeSlider = document.getElementById('aiVolume');
    const volumeControl = document.querySelector('.ai-volume-control');
    
    if (!volumeSlider || !volumeControl) return;
    
    // Initialize volume display
    updateVolumeSliderBackground(volumeSlider);
    
    // Add volume level indicator
    const volumeIndicator = document.createElement('span');
    volumeIndicator.className = 'volume-level';
    volumeIndicator.textContent = `${volumeSlider.value}%`;
    volumeControl.appendChild(volumeIndicator);
    
    // Add CSS for volume indicator
    const style = document.createElement('style');
    style.textContent = `
        .volume-level {
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8em;
            color: #2ecc71;
            font-weight: 600;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .ai-volume-control:hover .volume-level {
            opacity: 1;
            right: -35px;
        }
    `;
    document.head.appendChild(style);
    
    // Update volume display on input
    volumeSlider.addEventListener('input', () => {
        // Update volume indicator
        volumeIndicator.textContent = `${volumeSlider.value}%`;
        
        // Update slider background
        updateVolumeSliderBackground(volumeSlider);
        
        // Add muted class if volume is 0
        if (parseInt(volumeSlider.value) === 0) {
            volumeControl.classList.add('muted');
        } else {
            volumeControl.classList.remove('muted');
        }
        
        // Trigger volume change animation
        triggerVolumeChangeAnimation(volumeSlider);
    });
    
    // Double-click to reset to 70%
    volumeSlider.addEventListener('dblclick', () => {
        volumeSlider.value = 70;
        volumeIndicator.textContent = '70%';
        updateVolumeSliderBackground(volumeSlider);
        volumeControl.classList.remove('muted');
        
        // Create a new input event to trigger volume change
        const event = new Event('input', { bubbles: true });
        volumeSlider.dispatchEvent(event);
        
        // Show reset notification
        showVolumeNotification('Volume reset to 70%');
    });
    
    // Add mute toggle button
    const muteToggle = document.createElement('button');
    muteToggle.className = 'mute-toggle';
    muteToggle.innerHTML = '<span>🔊</span>';
    muteToggle.title = 'Mute/Unmute';
    volumeControl.appendChild(muteToggle);
    
    // Add CSS for mute toggle
    style.textContent += `
        .mute-toggle {
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 1.2em;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .mute-toggle:hover {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }
        
        .mute-toggle span {
            transition: all 0.3s ease;
        }
        
        .ai-volume-control.muted .mute-toggle span {
            content: '🔇';
            color: #e74c3c;
        }
    `;
    document.head.appendChild(style);
    
    // Mute toggle functionality
    let previousVolume = volumeSlider.value;
    muteToggle.addEventListener('click', () => {
        if (volumeControl.classList.contains('muted')) {
            // Unmute
            volumeSlider.value = previousVolume;
            volumeControl.classList.remove('muted');
            muteToggle.innerHTML = '<span>🔊</span>';
            showVolumeNotification('Sound unmuted');
        } else {
            // Mute
            previousVolume = volumeSlider.value;
            volumeSlider.value = 0;
            volumeControl.classList.add('muted');
            muteToggle.innerHTML = '<span>🔇</span>';
            showVolumeNotification('Sound muted');
        }
        
        // Update volume indicator
        volumeIndicator.textContent = `${volumeSlider.value}%`;
        
        // Update slider background
        updateVolumeSliderBackground(volumeSlider);
        
        // Create a new input event to trigger volume change
        const event = new Event('input', { bubbles: true });
        volumeSlider.dispatchEvent(event);
    });
    
    // Add keyboard shortcuts for volume control
    document.addEventListener('keydown', (e) => {
        // Only if we're on the activities page
        const activitiesPage = document.getElementById('activitiesPage');
        if (activitiesPage && activitiesPage.classList.contains('hidden')) return;
        
        // Up arrow: increase volume
        if (e.key === 'ArrowUp') {
            e.preventDefault();
            volumeSlider.value = Math.min(parseInt(volumeSlider.value) + 5, 100);
            volumeIndicator.textContent = `${volumeSlider.value}%`;
            updateVolumeSliderBackground(volumeSlider);
            volumeControl.classList.remove('muted');
            
            // Create a new input event to trigger volume change
            const event = new Event('input', { bubbles: true });
            volumeSlider.dispatchEvent(event);
            
            showVolumeNotification(`Volume: ${volumeSlider.value}%`);
        }
        
        // Down arrow: decrease volume
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            volumeSlider.value = Math.max(parseInt(volumeSlider.value) - 5, 0);
            volumeIndicator.textContent = `${volumeSlider.value}%`;
            updateVolumeSliderBackground(volumeSlider);
            
            // Add muted class if volume is 0
            if (parseInt(volumeSlider.value) === 0) {
                volumeControl.classList.add('muted');
            } else {
                volumeControl.classList.remove('muted');
            }
            
            // Create a new input event to trigger volume change
            const event = new Event('input', { bubbles: true });
            volumeSlider.dispatchEvent(event);
            
            showVolumeNotification(`Volume: ${volumeSlider.value}%`);
        }
        
        // M key: toggle mute
        if (e.key === 'm' || e.key === 'M') {
            e.preventDefault();
            muteToggle.click();
        }
    });
}

// Update volume slider background based on value
function updateVolumeSliderBackground(slider) {
    const value = slider.value;
    slider.style.background = `linear-gradient(to right, #2ecc71 0%, #2ecc71 ${value}%, #ecf0f1 ${value}%, #ecf0f1 100%)`;
}

// Trigger volume change animation
function triggerVolumeChangeAnimation(slider) {
    // Create ripple effect on slider
    const ripple = document.createElement('span');
    ripple.className = 'volume-ripple';
    
    // Position ripple at thumb position
    const thumbPosition = (slider.value / slider.max) * slider.clientWidth;
    ripple.style.left = `${thumbPosition}px`;
    
    // Add ripple to slider parent
    slider.parentNode.appendChild(ripple);
    
    // Add CSS for ripple
    if (!document.querySelector('.volume-ripple-style')) {
        const style = document.createElement('style');
        style.className = 'volume-ripple-style';
        style.textContent = `
            .ai-volume-control {
                position: relative;
                overflow: visible;
            }
            
            .volume-ripple {
                position: absolute;
                top: 50%;
                width: 20px;
                height: 20px;
                background: rgba(46, 204, 113, 0.3);
                border-radius: 50%;
                transform: translate(-50%, -50%) scale(0);
                animation: volumeRipple 0.6s ease-out;
                pointer-events: none;
            }
            
            @keyframes volumeRipple {
                to {
                    transform: translate(-50%, -50%) scale(3);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Remove ripple after animation
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Show volume notification
function showVolumeNotification(message) {
    // Remove existing notification
    const existingNotification = document.querySelector('.volume-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = 'volume-notification';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Add CSS for notification
    if (!document.querySelector('.volume-notification-style')) {
        const style = document.createElement('style');
        style.className = 'volume-notification-style';
        style.textContent = `
            .volume-notification {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(46, 204, 113, 0.9);
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-size: 0.9em;
                font-weight: 500;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                z-index: 1000;
                animation: notificationFade 2s forwards;
            }
            
            @keyframes notificationFade {
                0% { opacity: 0; transform: translateY(20px); }
                10% { opacity: 1; transform: translateY(0); }
                90% { opacity: 1; transform: translateY(0); }
                100% { opacity: 0; transform: translateY(-20px); }
            }
        `;
        document.head.appendChild(style);
    }
    
    // Remove notification after animation
    setTimeout(() => {
        notification.remove();
    }, 2000);
}
