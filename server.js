const express = require('express');
const cors = require('cors');
const axios = require('axios');
let ytdl;
// Try to use the more reliable @distube/ytdl-core first, fallback to regular ytdl-core
try {
    ytdl = require('@distube/ytdl-core');
    console.log('Using @distube/ytdl-core for better YouTube support');
} catch (error) {
    ytdl = require('ytdl-core');
    console.log('Using regular ytdl-core');
}
const youtubeSearchApi = require('youtube-search-api');
const { exec, spawn } = require('child_process');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const app = express();
const port = 3000;

// Promisify exec for easier use
const execAsync = promisify(exec);

// Check for Python dependencies on startup (with timeout)
console.log('Checking for Python dependencies...');
fs.access('youtube_audio.py', fs.constants.F_OK, (err) => {
    if (!err) {
        console.log('Python YouTube audio script found');
        // Test if Python dependencies are available with timeout
        exec('python -c "import yt_dlp, requests"', { timeout: 3000 }, (error, stdout, stderr) => {
            if (error) {
                console.warn('Python YouTube packages not fully available:', error.message);
                console.log('Install with: pip install yt-dlp requests');
            } else {
                console.log('Python YouTube packages detected');
            }
        });
    } else {
        console.log('Python YouTube audio script not found - using Node.js only');
    }
});
console.log('Python check initiated, continuing with server setup...');

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Configuration for AI models
const OLLAMA_BASE_URL = 'http://localhost:11434';
const LOCAL_MODEL_NAME = 'llama3.2:1b'; // Local Ollama model
const OPENROUTER_API_KEY = '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';
const OPENROUTER_MODEL = 'deepseek/deepseek-chat-v3-0324:free';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

// Model preference: 'auto', 'online', or 'offline'
// 'auto' will try online first, then fallback to offline
let MODEL_PREFERENCE = 'auto';

// Function to make OpenRouter API request
async function makeOpenRouterRequest(messages) {
    try {
        console.log('Making request to OpenRouter API...');

        const response = await axios.post(`${OPENROUTER_BASE_URL}/chat/completions`, {
            model: OPENROUTER_MODEL,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1000
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000', // Your site URL
                'X-Title': 'Stress AI Assistant' // Your site name
            },
            timeout: 15000 // 15 second timeout
        });

        console.log('OpenRouter API response received');
        return {
            success: true,
            data: response.data,
            model: 'online'
        };
    } catch (error) {
        console.error('OpenRouter API error:', error.message);
        return {
            success: false,
            error: error.message,
            model: 'online'
        };
    }
}

// Function to make Ollama API request
async function makeOllamaRequest(messages) {
    try {
        console.log('Making request to local Ollama API...');

        const response = await axios.post(`${OLLAMA_BASE_URL}/api/chat`, {
            model: LOCAL_MODEL_NAME,
            messages: messages,
            stream: false,
            options: {
                temperature: 0.7,
                top_p: 0.9
            }
        }, {
            timeout: 15000 // 15 second timeout for local model (reduced for better UX)
        });

        console.log('Ollama API response received');
        return {
            success: true,
            data: {
                choices: [{
                    message: {
                        content: response.data.message.content,
                        role: 'assistant'
                    }
                }],
                usage: response.data.usage || {}
            },
            model: 'offline'
        };
    } catch (error) {
        console.error('Ollama API error:', error.message);
        return {
            success: false,
            error: error.message,
            model: 'offline'
        };
    }
}

// Function to make AI request with fallback
async function makeAIRequest(messages, preference = 'auto') {
    if (preference === 'online') {
        return await makeOpenRouterRequest(messages);
    } else if (preference === 'offline') {
        return await makeOllamaRequest(messages);
    } else {
        // Auto mode: try online first, then offline
        const onlineResult = await makeOpenRouterRequest(messages);
        if (onlineResult.success) {
            return onlineResult;
        }

        console.log('Online AI failed, trying offline...');
        return await makeOllamaRequest(messages);
    }
}

// Function to enhance system prompt with stress data
function enhanceSystemPrompt(messages, stressData) {
    const systemPrompt = `You are a helpful AI assistant specializing in student stress management and well-being.

Current student stress context:
- Sleep: ${stressData.sleep} hours
- Study Hours: ${stressData.studyHours} hours
- Exercise: ${stressData.exercise} hours
- Social Time: ${stressData.socialTime} hours
- Screen Time: ${stressData.screenTime} hours
- Stress Level: ${stressData.stressLevel}/10
- Mood: ${stressData.mood}

Please provide personalized advice considering this context. Be empathetic, supportive, and offer practical suggestions for stress management and well-being improvement.`;

    // Find existing system message or add new one
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    if (systemMessageIndex >= 0) {
        messages[systemMessageIndex].content = systemPrompt + '\n\n' + messages[systemMessageIndex].content;
    } else {
        messages.unshift({ role: 'system', content: systemPrompt });
    }

    return messages;
}

// Rule-based fallback for stress prediction
function generateFallbackStressPrediction(data) {
    const { sleep, studyHours, exercise, socialTime, screenTime } = data;

    let stressScore = 0;
    let factors = [];
    let advice = [];

    // Sleep analysis
    if (sleep < 6) {
        stressScore += 3;
        factors.push('Insufficient sleep');
        advice.push('Try to get 7-9 hours of sleep per night for better stress management');
    } else if (sleep > 9) {
        stressScore += 1;
        factors.push('Excessive sleep');
        advice.push('Consider maintaining a consistent sleep schedule');
    }

    // Study hours analysis
    if (studyHours > 8) {
        stressScore += 2;
        factors.push('High study load');
        advice.push('Take regular breaks during study sessions using the Pomodoro technique');
    } else if (studyHours < 2) {
        stressScore += 1;
        factors.push('Low study engagement');
        advice.push('Consider creating a structured study schedule');
    }

    // Exercise analysis
    if (exercise < 0.5) {
        stressScore += 2;
        factors.push('Lack of physical activity');
        advice.push('Try to incorporate at least 30 minutes of physical activity daily');
    }

    // Social time analysis
    if (socialTime < 1) {
        stressScore += 1;
        factors.push('Limited social interaction');
        advice.push('Make time for social connections - they are important for mental health');
    }

    // Screen time analysis
    if (screenTime > 6) {
        stressScore += 1;
        factors.push('High screen time');
        advice.push('Consider reducing recreational screen time and taking regular digital breaks');
    }

    // Determine stress level
    let stressLevel, stressCategory;
    if (stressScore <= 2) {
        stressLevel = 'Low';
        stressCategory = 'low';
    } else if (stressScore <= 5) {
        stressLevel = 'Moderate';
        stressCategory = 'moderate';
    } else {
        stressLevel = 'High';
        stressCategory = 'high';
    }

    // Add general advice
    advice.push('Practice mindfulness or meditation for 10-15 minutes daily');
    advice.push('Stay hydrated and maintain a balanced diet');

    return {
        stressLevel: stressCategory,
        stressScore: Math.min(stressScore, 10),
        factors: factors.length > 0 ? factors : ['General daily stressors'],
        advice: advice,
        confidence: 0.75,
        source: 'rule-based-fallback'
    };
}

// API endpoint for model status
app.get('/api/model/status', async (req, res) => {
    try {
        // Test online model
        const onlineTest = await makeOpenRouterRequest([
            { role: 'user', content: 'Hello' }
        ]);

        // Test offline model
        const offlineTest = await makeOllamaRequest([
            { role: 'user', content: 'Hello' }
        ]);

        res.json({
            online: {
                available: onlineTest.success,
                model: OPENROUTER_MODEL,
                error: onlineTest.error || null
            },
            offline: {
                available: offlineTest.success,
                model: LOCAL_MODEL_NAME,
                error: offlineTest.error || null
            },
            current_preference: MODEL_PREFERENCE
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to check model status',
            details: error.message
        });
    }
});

// API endpoint to change model preference
app.post('/api/model/preference', (req, res) => {
    try {
        const { preference } = req.body;

        if (!['auto', 'online', 'offline'].includes(preference)) {
            return res.status(400).json({
                error: 'Invalid preference',
                details: 'Preference must be one of: auto, online, offline'
            });
        }

        MODEL_PREFERENCE = preference;
        console.log(`Model preference changed to: ${preference}`);

        res.json({
            success: true,
            preference: MODEL_PREFERENCE
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to change model preference',
            details: error.message
        });
    }
});

// API endpoint for chat completions
app.post('/api/chat/completions', async (req, res) => {
    try {
        const { messages, stressData, modelPreference } = req.body;

        // Enhanced messages with stress data if available
        const enhancedMessages = stressData ? enhanceSystemPrompt([...messages], stressData) : messages;

        // Log the enhanced messages for debugging
        console.log('Enhanced messages with stress data:', JSON.stringify(enhancedMessages, null, 2));

        // Use specified model preference or default
        const preference = modelPreference || MODEL_PREFERENCE;
        console.log(`Using model preference: ${preference}`);

        // Make AI request with fallback
        const result = await makeAIRequest(enhancedMessages, preference);

        if (result.success) {
            res.json({
                success: true,
                data: result.data,
                model_used: result.model
            });
        } else {
            res.status(500).json({
                success: false,
                error: result.error,
                model_attempted: result.model
            });
        }
    } catch (error) {
        console.error('Error in chat completions endpoint:', error);
        res.status(500).json({
            error: 'Failed to get AI response',
            details: error.message
        });
    }
});

// Dedicated endpoint for stress prediction with enhanced fallback
app.post('/api/stress/predict', async (req, res) => {
    try {
        const { studentData } = req.body;

        if (!studentData) {
            return res.status(400).json({
                error: 'Student data is required',
                details: 'Please provide student data for stress prediction'
            });
        }

        console.log('Stress prediction request received:', studentData);

        // Create AI prompt for stress prediction
        const messages = [{
            role: 'system',
            content: `You are an AI assistant specialized in student stress analysis. Analyze the provided student data and return a JSON response with stress prediction.

Required JSON format:
{
    "stressLevel": "low|moderate|high",
    "stressScore": number (1-10),
    "factors": ["factor1", "factor2"],
    "advice": ["advice1", "advice2"],
    "confidence": number (0-1)
}

Consider sleep quality, study load, exercise, social time, and screen time in your analysis.`
        }, {
            role: 'user',
            content: `Please analyze this student data and provide stress prediction:
Sleep: ${studentData.sleep} hours
Study Hours: ${studentData.studyHours} hours
Exercise: ${studentData.exercise} hours
Social Time: ${studentData.socialTime} hours
Screen Time: ${studentData.screenTime} hours
Current Mood: ${studentData.mood || 'Not specified'}`
        }];

        try {
            // Try AI prediction first
            const result = await makeAIRequest(messages, MODEL_PREFERENCE);

            if (result.success) {
                console.log('AI stress prediction successful');

                // Try to parse the AI response
                try {
                    const jsonMatch = result.data.choices[0].message.content.match(/\{[\s\S]*\}/);
                    const jsonString = jsonMatch ? jsonMatch[0] : result.data.choices[0].message.content;
                    const prediction = JSON.parse(jsonString);

                    // Validate the prediction
                    if (prediction.stressLevel && prediction.advice) {
                        return res.json({
                            success: true,
                            prediction: prediction,
                            source: 'ai',
                            model_used: result.model
                        });
                    } else {
                        throw new Error('Invalid AI response format');
                    }
                } catch (parseError) {
                    console.warn('Failed to parse AI response, using fallback');
                    throw parseError;
                }
            } else {
                throw new Error('AI prediction failed');
            }
        } catch (aiError) {
            console.log('AI prediction failed, using rule-based fallback:', aiError.message);

            // Use rule-based fallback
            const fallbackPrediction = generateFallbackStressPrediction(studentData);

            return res.json({
                success: true,
                prediction: fallbackPrediction,
                source: 'fallback',
                message: 'AI temporarily unavailable, using intelligent fallback system'
            });
        }

    } catch (error) {
        console.error('Error in stress prediction endpoint:', error);
        res.status(500).json({
            error: 'Failed to predict stress level',
            details: error.message
        });
    }
});

// YouTube API endpoints
app.get('/api/youtube/search', async (req, res) => {
    try {
        const { query = 'relaxing music', limit = 15, filter = 'music' } = req.query;

        // Add category filter for better music results
        let searchQuery = query;
        if (filter === 'music' && !query.includes('music')) {
            searchQuery = `${query} music`;
        }

        console.log(`Searching YouTube for: "${searchQuery}" with limit ${limit}`);
        const searchResults = await youtubeSearchApi.GetListByKeyword(searchQuery, false, limit);

        // Log the number of results found
        if (searchResults && searchResults.items) {
            console.log(`Found ${searchResults.items.length} YouTube results`);
        }

        res.json(searchResults);
    } catch (error) {
        console.error('Error searching YouTube:', error);
        res.status(500).json({ error: 'Failed to search YouTube', details: error.message });
    }
});

// Extract video ID from various YouTube URL formats
app.get('/api/youtube/extract-id', (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        // Check if it's already a video ID (11 characters)
        if (/^[a-zA-Z0-9_-]{11}$/.test(url)) {
            return res.json({ videoId: url });
        }

        // Try to extract using ytdl-core
        const videoId = ytdl.getVideoID(url);
        return res.json({ videoId });
    } catch (error) {
        console.error('Error extracting YouTube video ID:', error);
        res.status(400).json({
            error: 'Invalid YouTube URL',
            details: error.message
        });
    }
});

// Get video info for YouTube videos
app.get('/api/youtube/video-info/:videoId', async (req, res) => {
    try {
        const { videoId } = req.params;
        console.log('Fetching audio info for YouTube video:', videoId);

        // Try Python-based extraction first if available
        try {
            const pythonResult = await execAsync(`python youtube_audio.py --url ${videoId} --info`);
            if (pythonResult && pythonResult.stdout) {
                const audioInfo = JSON.parse(pythonResult.stdout);

                if (!audioInfo.error) {
                    console.log('Successfully extracted audio info using Python');
                    return res.json(audioInfo);
                }
            }
        } catch (pythonError) {
            console.warn('Python audio extraction failed, trying Node.js method:', pythonError.message);
        }

        // Fallback to Node.js method
        try {
            const info = await ytdl.getInfo(videoId);
            const audioFormats = ytdl.filterFormats(info.formats, 'audioonly');

            if (audioFormats.length > 0) {
                const bestAudio = audioFormats[0];
                return res.json({
                    success: true,
                    title: info.videoDetails.title,
                    duration: info.videoDetails.lengthSeconds,
                    audioUrl: bestAudio.url,
                    quality: bestAudio.audioBitrate || 'unknown',
                    format: bestAudio.container || 'unknown'
                });
            } else {
                throw new Error('No audio formats available');
            }
        } catch (nodeError) {
            console.error('Node.js audio extraction failed:', nodeError.message);
            throw nodeError;
        }

    } catch (error) {
        console.error('Error getting YouTube video info:', error);

        let errorMessage = 'Failed to get video information';
        let statusCode = 500;

        if (error.message.includes('Video unavailable')) {
            errorMessage = 'Video is unavailable or private';
            statusCode = 404;
        } else if (error.message.includes('age-restricted')) {
            errorMessage = 'Video is age-restricted';
            statusCode = 403;
        } else if (error.message.includes('copyright')) {
            errorMessage = 'Video has copyright restrictions';
            statusCode = 403;
        }

        res.status(statusCode).json({
            error: errorMessage,
            details: error.message,
            suggestion: 'Try using a different YouTube video or use the built-in relaxation music instead.'
        });
    }
});

// Add a proxy endpoint for YouTube audio to avoid CORS issues
app.get('/api/youtube/audio-proxy', async (req, res) => {
    try {
        const { url, videoId } = req.query;

        // Handle direct videoId input
        if (videoId && !url) {
            console.log('Getting audio stream for video ID:', videoId);
            try {
                const info = await ytdl.getInfo(videoId);
                const audioFormats = ytdl.filterFormats(info.formats, 'audioonly');

                if (audioFormats.length > 0) {
                    const bestAudio = audioFormats[0];
                    return res.redirect(bestAudio.url);
                } else {
                    throw new Error('No audio formats available');
                }
            } catch (error) {
                console.error('Error getting video info for proxy:', error);
                return res.status(500).json({
                    error: 'Failed to get video audio stream',
                    details: error.message
                });
            }
        }

        if (!url) {
            return res.status(400).json({ error: 'URL or videoId parameter is required' });
        }

        console.log('Proxying audio from URL:', url.substring(0, 100) + '...');

        // Make request to the YouTube audio URL
        const response = await axios({
            method: 'GET',
            url: url,
            responseType: 'stream',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'audio/*,*/*;q=0.9',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'identity',
                'Range': req.headers.range || 'bytes=0-'
            },
            timeout: 10000
        });

        // Set appropriate headers for audio streaming
        res.set('Content-Type', response.headers['content-type'] || 'audio/mp4');
        res.set('Content-Length', response.headers['content-length']);
        res.set('Accept-Ranges', 'bytes');
        res.set('Cache-Control', 'public, max-age=3600');

        // Pipe the audio stream to the response
        response.data.pipe(res);
    } catch (error) {
        console.error('Error proxying audio:', error);
        res.status(500).json({
            error: 'Failed to proxy audio',
            details: error.message
        });
    }
});

// Add a direct streaming endpoint for YouTube audio
app.get('/api/youtube/stream/:videoId', async (req, res) => {
    try {
        const { videoId } = req.params;
        const { quality = 'highestaudio', format = 'mp4' } = req.query;

        console.log(`Streaming YouTube video ${videoId} with quality ${quality} and format preference ${format}`);

        // Try Python-based extraction first if available
        try {
            const pythonResult = await execAsync(`python youtube_audio.py --url ${videoId} --stream --quality ${quality} --format ${format}`);
            if (pythonResult && pythonResult.stdout) {
                const streamInfo = JSON.parse(pythonResult.stdout);

                if (streamInfo.streamUrl && !streamInfo.error) {
                    console.log('Using Python-based streaming');
                    return res.redirect(streamInfo.streamUrl);
                }
            }
        } catch (pythonError) {
            console.warn('Python streaming failed, using Node.js method:', pythonError.message);
        }

        // Fallback to Node.js method
        const info = await ytdl.getInfo(videoId);
        const audioFormats = ytdl.filterFormats(info.formats, 'audioonly');

        // Filter by format preference if specified
        let filteredFormats = audioFormats;
        if (format && format !== 'any') {
            filteredFormats = audioFormats.filter(f =>
                f.container === format ||
                (f.mimeType && f.mimeType.includes(format))
            );
        }

        // If no formats match the preference, use all audio formats
        if (filteredFormats.length === 0) {
            filteredFormats = audioFormats;
        }

        if (filteredFormats.length > 0) {
            // Sort by quality (higher bitrate first)
            filteredFormats.sort((a, b) => {
                const bitrateA = a.audioBitrate || 0;
                const bitrateB = b.audioBitrate || 0;
                return bitrateB - bitrateA;
            });

            const bestFormat = filteredFormats[0];
            console.log(`Selected format: ${bestFormat.container}, bitrate: ${bestFormat.audioBitrate}`);

            // Redirect to the audio URL
            res.redirect(bestFormat.url);
        } else {
            throw new Error('No suitable audio formats found');
        }

    } catch (error) {
        console.error('Error streaming YouTube audio:', error);

        let errorMessage = 'Failed to stream audio';
        let statusCode = 500;

        if (error.message.includes('Video unavailable')) {
            errorMessage = 'Video is unavailable or private';
            statusCode = 404;
        } else if (error.message.includes('age-restricted')) {
            errorMessage = 'Video is age-restricted';
            statusCode = 403;
        }

        res.status(statusCode).json({
            error: errorMessage,
            details: error.message
        });
    }
});

// Add an endpoint to validate YouTube URLs using Python
app.get('/api/youtube/validate', async (req, res) => {
    try {
        const { url } = req.query;

        if (!url) {
            return res.status(400).json({ error: 'URL parameter is required' });
        }

        // Try to validate using Python first
        try {
            const pythonResult = await execAsync(`python youtube_audio.py --url ${url} --validate`);
            if (pythonResult && pythonResult.stdout) {
                const validationResult = JSON.parse(pythonResult.stdout);
                return res.json(validationResult);
            }
        } catch (pythonError) {
            console.warn('Python validation failed, falling back to Node.js method:', pythonError.message);
        }

        // Fallback to Node.js validation
        try {
            const videoId = ytdl.getVideoID(url);
            const info = await ytdl.getBasicInfo(videoId);

            res.json({
                valid: true,
                videoId: videoId,
                title: info.videoDetails.title,
                duration: info.videoDetails.lengthSeconds,
                author: info.videoDetails.author.name
            });
        } catch (error) {
            res.json({
                valid: false,
                error: error.message
            });
        }

    } catch (error) {
        console.error('Error validating YouTube URL:', error);
        res.status(500).json({
            error: 'Failed to validate URL',
            details: error.message
        });
    }
});

// Start server
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`AI Configuration:`);
    console.log(`  Model Preference: ${MODEL_PREFERENCE}`);
    console.log(`  Online Model: ${OPENROUTER_MODEL} (OpenRouter)`);
    console.log(`  Offline Model: ${LOCAL_MODEL_NAME} (Ollama)`);
    console.log(`  API Endpoints:`);
    console.log(`    - GET /api/model/status - Check model availability`);
    console.log(`    - POST /api/model/preference - Change model preference`);
    console.log(`    - POST /api/stress/predict - Predict stress levels`);
    console.log(`    - POST /api/chat/completions - AI chat completions`);
    console.log(`    - GET /api/youtube/search - Search YouTube videos`);
    console.log(`    - GET /api/youtube/video-info/:videoId - Get video information`);
});