<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Model Selector Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .model-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
            padding: 10px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(52, 152, 219, 0.2);
        }
        .model-select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            font-size: 0.9em;
            cursor: pointer;
        }
        .model-status {
            font-size: 0.8em;
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .model-status.online { background: #2ecc71; color: white; }
        .model-status.offline { background: #3498db; color: white; }
        .model-status.checking { background: #f39c12; color: white; }
        .model-status.error { background: #e74c3c; color: white; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
        }
        .error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .success {
            border-left-color: #2ecc71;
            background: #f0f9f4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Model Selector Test</h1>
        
        <div class="model-selector">
            <label for="modelPreference">AI Model:</label>
            <select id="modelPreference" class="model-select">
                <option value="auto">Auto (Best Available)</option>
                <option value="online">Online (DeepSeek)</option>
                <option value="offline">Offline (Llama 3.2)</option>
            </select>
            <span id="modelStatus" class="model-status checking">Checking...</span>
        </div>

        <div class="test-section">
            <h3>Model Status Test</h3>
            <button class="test-button" onclick="checkModelStatus()">Check Model Status</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Model Preference Test</h3>
            <button class="test-button" onclick="testPreference('auto')">Set Auto</button>
            <button class="test-button" onclick="testPreference('online')">Set Online</button>
            <button class="test-button" onclick="testPreference('offline')">Set Offline</button>
            <div id="preferenceResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Chat Test</h3>
            <button class="test-button" onclick="testChat()">Test Chat Response</button>
            <div id="chatResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Check model status
        async function checkModelStatus() {
            const resultDiv = document.getElementById('statusResult');
            const modelStatus = document.getElementById('modelStatus');
            const modelPreference = document.getElementById('modelPreference');
            
            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = 'Checking model status...';
                
                const response = await fetch('/api/model/status');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const status = await response.json();
                
                // Update UI
                modelPreference.value = status.current_preference;
                
                let statusText = '';
                let statusClass = '';
                
                if (status.current_preference === 'auto') {
                    if (status.online_model.status === 'available') {
                        statusText = 'Auto → Online (DeepSeek)';
                        statusClass = 'online';
                    } else if (status.offline_model.status === 'available') {
                        statusText = 'Auto → Offline (Llama)';
                        statusClass = 'offline';
                    } else {
                        statusText = 'No models available';
                        statusClass = 'error';
                    }
                } else if (status.current_preference === 'online') {
                    statusText = status.online_model.status === 'available' ? 'Online (DeepSeek)' : 'Online unavailable';
                    statusClass = status.online_model.status === 'available' ? 'online' : 'error';
                } else if (status.current_preference === 'offline') {
                    statusText = status.offline_model.status === 'available' ? 'Offline (Llama)' : 'Offline unavailable';
                    statusClass = status.offline_model.status === 'available' ? 'offline' : 'error';
                }
                
                modelStatus.textContent = statusText;
                modelStatus.className = `model-status ${statusClass}`;
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Model Status Retrieved:</strong><br>
                    Current Preference: ${status.current_preference}<br>
                    Online Model (${status.online_model.name}): ${status.online_model.status}<br>
                    Offline Model (${status.offline_model.name}): ${status.offline_model.status}
                `;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
                modelStatus.textContent = 'Status check failed';
                modelStatus.className = 'model-status error';
            }
        }

        // Test preference change
        async function testPreference(preference) {
            const resultDiv = document.getElementById('preferenceResult');
            
            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = `Setting preference to ${preference}...`;
                
                const response = await fetch('/api/model/preference', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ preference })
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const result = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Preference Updated:</strong><br>
                    ${result.message}<br>
                    Old: ${result.old_preference} → New: ${result.new_preference}
                `;
                
                // Update status after change
                setTimeout(checkModelStatus, 500);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
            }
        }

        // Test chat
        async function testChat() {
            const resultDiv = document.getElementById('chatResult');
            const modelPreference = document.getElementById('modelPreference');
            
            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = 'Testing chat response...';
                
                const response = await fetch('/api/chat/completions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        messages: [
                            { role: 'user', content: 'Hello! Please respond with "Test successful" to confirm you are working.' }
                        ],
                        modelPreference: modelPreference.value
                    })
                });
                
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const result = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Chat Response:</strong><br>
                    Model Used: ${result.model_used || 'unknown'}<br>
                    Model Name: ${result.model_name || 'unknown'}<br>
                    Response: "${result.content}"
                `;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            checkModelStatus();
            
            // Handle preference selector change
            document.getElementById('modelPreference').addEventListener('change', (e) => {
                testPreference(e.target.value);
            });
        });
    </script>
</body>
</html>
