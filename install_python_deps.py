+0#!/usr/bin/env python3
"""
Install Python dependencies for YouTube audio functionality
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    print("🎵 Installing Python dependencies for YouTube audio functionality...")
    print("=" * 60)
    
    # List of required packages
    packages = [
        "yt-dlp",           # Primary YouTube downloader (recommended)
        "pytube",           # Backup YouTube downloader
        "requests",         # For HTTP requests
        "ffmpeg-python"     # For audio processing (optional)
    ]
    
    success_count = 0
    total_packages = len(packages)
    
    for package in packages:
        print(f"\n📦 Installing {package}...")
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"Installation complete: {success_count}/{total_packages} packages installed successfully")
    
    if success_count == total_packages:
        print("🎉 All dependencies installed successfully!")
        print("\nYour YouTube audio functionality should now work properly.")
    else:
        print("⚠️  Some packages failed to install.")
        print("The app will still work but may have limited functionality.")
    
    print("\n📝 Note: You may also need to install FFmpeg separately for audio conversion:")
    print("   - Windows: Download from https://ffmpeg.org/download.html")
    print("   - macOS: brew install ffmpeg")
    print("   - Linux: sudo apt install ffmpeg (Ubuntu/Debian) or equivalent")

if __name__ == "__main__":
    main()
