/* animations.css - Enhanced animations for StressAI */

/* ===== GLOBAL ANIMATIONS ===== */

/* Smooth page transitions */
.page {
    transition: opacity 0.4s ease, transform 0.4s ease;
    will-change: opacity, transform;
}

.page.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

.page:not(.hidden) {
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Transition for cards (no floating) */
.activity-card {
    transition: all 0.3s ease;
}

/* Pulse animation for buttons */
.activity-btn, #predictButton, .save-button, .play-btn {
    position: relative;
    overflow: hidden;
}

.activity-btn:after, #predictButton:after, .save-button:after, .play-btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.activity-btn:focus:not(:active):after,
#predictButton:focus:not(:active):after,
.save-button:focus:not(:active):after,
.play-btn:focus:not(:active):after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* Ambient background animation */
body:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background: radial-gradient(circle at 10% 10%, rgba(52, 152, 219, 0.03), transparent 40%),
                radial-gradient(circle at 90% 90%, rgba(46, 204, 113, 0.03), transparent 40%);
    z-index: -1;
    opacity: 0.7;
    animation: ambientShift 15s ease-in-out infinite alternate;
}

@keyframes ambientShift {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 100% 100%;
    }
}

/* ===== SPECIFIC ELEMENT ANIMATIONS ===== */

/* Breathing circle enhanced animation */
.breathing-animation {
    box-shadow: 0 0 0 rgba(52, 152, 219, 0.4);
}

.breathing-animation.inhale {
    animation: inhale 4s ease-in-out;
    box-shadow: 0 0 30px rgba(46, 204, 113, 0.6);
}

.breathing-animation.exhale {
    animation: exhale 4s ease-in-out;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.4);
}

@keyframes inhale {
    from {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.4);
    }
    to {
        transform: scale(1.5);
        box-shadow: 0 0 30px rgba(46, 204, 113, 0.6);
    }
}

@keyframes exhale {
    from {
        transform: scale(1.5);
        box-shadow: 0 0 30px rgba(46, 204, 113, 0.6);
    }
    to {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.4);
    }
}

/* Enhanced progress bar animation */
.progress-fill {
    background: linear-gradient(90deg, #3498db, #2ecc71);
    background-size: 200% 100%;
    animation: gradientShift 2s linear infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}

/* Stress meter animation */
#stressMeterFill {
    position: relative;
}

#stressMeterFill:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
                rgba(255,255,255,0) 0%,
                rgba(255,255,255,0.3) 50%,
                rgba(255,255,255,0) 100%);
    animation: shimmerEffect 1.5s infinite;
}

@keyframes shimmerEffect {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Nav item hover effect */
.nav-item {
    position: relative;
}

.nav-item:after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #3498db;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-item:hover:after {
    width: 70%;
}

.nav-item.active:after {
    width: 70%;
}

/* Profile avatar pulse */
.profile-btn {
    animation: subtle-pulse 3s ease-in-out infinite;
}

@keyframes subtle-pulse {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

/* Input range slider animation */
input[type="range"] {
    position: relative;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #3498db;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 0 0 rgba(52, 152, 219, 0.4);
}

input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.6);
}

/* Volume control animation */
.ai-volume-control input[type="range"]::-webkit-slider-thumb {
    background: #2ecc71;
}

/* Typing animation for chat input */
.chat-input textarea:focus {
    animation: borderPulse 2s infinite;
}

@keyframes borderPulse {
    0% { border-color: rgba(52, 152, 219, 0.3); }
    50% { border-color: rgba(52, 152, 219, 0.8); }
    100% { border-color: rgba(52, 152, 219, 0.3); }
}

/* Enhanced modal animations */
.modal {
    transition: opacity 0.3s ease;
    opacity: 0;
    pointer-events: none;
}

.modal.show {
    opacity: 1;
    pointer-events: auto;
    animation: modalFadeIn 0.3s forwards;
}

.modal-content {
    transform: scale(0.8);
    opacity: 0;
    transition: all 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
    opacity: 1;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Music player animations */
.ai-track-player {
    position: relative;
    overflow: hidden;
}

.ai-track-player:after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, transparent 70%);
    animation: musicPulse 4s ease-in-out infinite;
}

@keyframes musicPulse {
    0% { transform: scale(0.8); opacity: 0.3; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(0.8); opacity: 0.3; }
}
